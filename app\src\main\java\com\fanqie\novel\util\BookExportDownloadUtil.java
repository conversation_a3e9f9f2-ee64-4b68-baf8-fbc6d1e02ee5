package com.fanqie.novel.util;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import androidx.activity.result.ActivityResultLauncher;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.fanqie.novel.data.db.mapper.BookMapper;
import com.fanqie.novel.data.model.BookInfo;

/**
 * 书籍导出和下载工具类
 * 抽取书架页和详情页共用的导出和下载功能
 */
public class BookExportDownloadUtil {
    private static final String TAG = "BookExportDownloadUtil";
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    /**
     * 导出书籍
     *
     * @param context 上下文
     * @param book 书籍信息
     * @param saveFileLauncher 文件保存启动器
     * @param swipeRefreshLayout 下拉刷新布局（可为null）
     * @param exportCallback 导出回调
     */
    public static void exportBook(
            Context context, 
            BookInfo book, 
            ActivityResultLauncher<Intent> saveFileLauncher,
            SwipeRefreshLayout swipeRefreshLayout,
            ExportCallback exportCallback) {
        
        String bookId = book.getBookId();
        String bookName = book.getBookName();
        
        // 使用BookDataExportUtil导出书籍
        String filename = bookName + ".txt";

        // 显示进度指示
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setRefreshing(true);
        }

        // 获取书籍数据并创建临时文件
        BookDataExportUtil.getBookDataAndCreateTempFile(context, bookId, filename)
                .thenAccept(tempFile -> {
                    mainHandler.post(() -> {
                        if (swipeRefreshLayout != null) {
                            swipeRefreshLayout.setRefreshing(false);
                        }
                        if (tempFile != null) {
                            Intent intent = BookDataExportUtil.createSaveIntent(filename, tempFile);
                            saveFileLauncher.launch(intent);

                            // 更新书籍状态为已导出
                            BookMapper.getInstance(context).updateStatusByBookId(bookId, 2);
                            ToastUtil.showSuccessToast(context, "《" + bookName + "》导出成功");
                            if (exportCallback != null) {
                                exportCallback.onExportSuccess(book);
                            }
                        } else {
                            ToastUtil.showCustomToast(context, "导出失败，无法创建文件");
                            if (exportCallback != null) {
                                exportCallback.onExportFailed(book, "无法创建文件");
                            }
                        }
                    });
                })
                .exceptionally(e -> {
                    mainHandler.post(() -> {
                        if (swipeRefreshLayout != null) {
                            swipeRefreshLayout.setRefreshing(false);
                        }
                        String errorMsg = e.getMessage();
                        ToastUtil.showCustomToast(context, "导出失败: " + errorMsg);
                        LogUtil.e(TAG, "导出书籍失败", e);
                        if (exportCallback != null) {
                            exportCallback.onExportFailed(book, errorMsg);
                        }
                    });
                    return null;
                });
    }

    /**
     * 导出回调接口
     */
    public interface ExportCallback {
        void onExportSuccess(BookInfo book);
        void onExportFailed(BookInfo book, String errorMsg);
    }
} 