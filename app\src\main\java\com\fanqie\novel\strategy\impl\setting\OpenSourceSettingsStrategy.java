package com.fanqie.novel.strategy.impl.setting;

import android.app.Activity;

import com.fanqie.novel.constants.DownloadStrategyType;
import com.fanqie.novel.strategy.ISettingsStrategy;
import com.fanqie.novel.strategy.impl.down.OpenSourceDownloadStrategy;
import com.fanqie.novel.util.DownloadConfigManager;
import com.fanqie.novel.util.ToastUtil;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class OpenSourceSettingsStrategy implements ISettingsStrategy {
    @Override
    public void getConfig(Activity activity, Callback callback) {
        try {
            DownloadConfigManager configManager = DownloadConfigManager.getInstance(activity);
            JSONObject py = configManager.getConfig(DownloadStrategyType.PYTHON_LIKE);
            Map<String, Object> config = new HashMap<>();
            config.put("auth_token", py.optString("auth_token", ""));
            config.put("server_url", py.optString("server_url", ""));
            config.put("max_batch_size", py.optInt("max_batch_size", 10));
            config.put("name", py.optString("name", "qyuing"));
            callback.onSuccess(config);
        } catch (Exception e) {
            callback.onFailure("加载Python模式配置失败: " + e.getMessage());
        }
    }

    @Override
    public void updateConfig(Activity activity, Map<String, Object> values, Callback callback) {
        try {
            DownloadConfigManager configManager = DownloadConfigManager.getInstance(activity);
            JSONObject py = new JSONObject()
                .put("auth_token", values.get("auth_token"))
                .put("server_url", values.get("server_url"))
                .put("max_batch_size", values.get("max_batch_size"))
                .put("name", values.get("name"));
            configManager.setConfig(DownloadStrategyType.PYTHON_LIKE, py);
            callback.onSuccess(values);
        } catch (Exception e) {
            callback.onFailure("保存Python模式配置失败: " + e.getMessage());
        }
    }

    @Override
    public void onSuccess(Activity activity) {
        // 弹Toast，拉取API端点，关闭设置页
        ToastUtil.showCustomToast(activity, "设置已保存，正在刷新API端点");
        // 拉取API端点并刷新全局配置
        DownloadConfigManager configManager = DownloadConfigManager.getInstance(activity);
        String serverUrl = configManager.getServerUrl();
        String authToken = configManager.getAuthToken();
        new OpenSourceDownloadStrategy().fetchApiEndpointsFromServer(serverUrl, authToken, (success, endpoints, errorMsg) -> {
            if (success) {
                ToastUtil.showCustomToast(activity, "API端点刷新成功");
            } else {
                ToastUtil.showCustomToast(activity, "API端点刷新失败: " + errorMsg);
            }
            activity.finish();
        });
    }

    @Override
    public void onFailure(Activity activity, String errorMsg) {
        ToastUtil.showCustomToast(activity, errorMsg);
    }
} 