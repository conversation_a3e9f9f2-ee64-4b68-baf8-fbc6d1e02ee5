package com.fanqie.novel.data.model;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2024/11/16 18:16
 * @description BookDataDTO
 */
public class BookDataDTO {
    @J<PERSON>NField(name = "abstract")
    private String abstractX;
    @<PERSON><PERSON><PERSON>ield(name = "author")
    private String author;
    @J<PERSON><PERSON>ield(name = "book_id")
    private String bookId;
    @JSONField(name = "book_name")
    private String bookName;
    @JSONField(name = "category")
    private String category;
    @JSONField(name = "source")
    private String source;
    @J<PERSON>NField(name = "tags")
    private String tags;
    @JSONField(name = "last_publish_time")
    private String lastPublishTime;
    @JSONField(name = "thumb_url")
    private String thumbUrl;
    @JSO<PERSON>ield(name = "word_number")
    private String wordNumber;

    public String getAbstractX() {
        return abstractX;
    }

    public void setAbstractX(String abstractX) {
        this.abstractX = abstractX;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getLastPublishTime() {
        return lastPublishTime;
    }

    public void setLastPublishTime(String lastPublishTime) {
        this.lastPublishTime = lastPublishTime;
    }

    public String getThumbUrl() {
        return thumbUrl;
    }

    public void setThumbUrl(String thumbUrl) {
        this.thumbUrl = thumbUrl;
    }

    public String getWordNumber() {
        return wordNumber;
    }

    public void setWordNumber(String wordNumber) {
        this.wordNumber = wordNumber;
    }
}
