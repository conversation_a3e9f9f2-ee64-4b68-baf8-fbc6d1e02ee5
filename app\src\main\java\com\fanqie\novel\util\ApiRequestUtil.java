package com.fanqie.novel.util;

import com.fanqie.novel.data.model.ApiEndpoint;

import java.util.Map;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;

/**
 * API请求工具类
 * 提供不同类型API请求的创建和响应时间格式化功能
 */
public class ApiRequestUtil {

    /**
     * 为fanqie_sdk API创建POST请求
     * @param endpoint API端点信息
     * @param chapterId 章节ID
     * @param userAgent User-Agent字符串
     * @return HTTP响应对象，失败时返回null
     */
    public static HttpResponse makeFanqieSdkRequest(ApiEndpoint endpoint, String chapterId, String userAgent) {
        try {
            String url = endpoint.getUrl(); // fanqie_sdk的URL不需要替换chapter_id

            // 获取params参数（作为URL查询参数）
            Map<String, Object> params = endpoint.getParams();
            if (params == null) {
                // 使用默认参数
                params = new java.util.HashMap<>();
                params.put("sdk_type", "4");
                params.put("novelsdk_aid", "638505");
            }

            // 获取data参数（作为POST请求体）
            Map<String, Object> data = endpoint.getData();
            if (data == null) {
                // 使用默认数据
                data = new java.util.HashMap<>();
                data.put("item_id", chapterId);
                data.put("need_book_info", 1);
                data.put("show_picture", 1);
                data.put("sdk_type", 1);
            } else {
                // 确保item_id被正确设置
                data = new java.util.HashMap<>(data);
                data.put("item_id", chapterId);
            }

            // 构建带查询参数的URL
            StringBuilder urlBuilder = new StringBuilder(url);
            if (!params.isEmpty()) {
                urlBuilder.append("?");
                boolean first = true;
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    if (!first) {
                        urlBuilder.append("&");
                    }
                    urlBuilder.append(entry.getKey()).append("=").append(entry.getValue());
                    first = false;
                }
            }
            String finalUrl = urlBuilder.toString();

            // 创建POST请求
            HttpRequest request = HttpUtil.createPost(finalUrl)
                    .header("User-Agent", userAgent)
                    .header("Accept", "application/json, text/javascript, */*; q=0.01")
                    .header("Accept-Language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7")
                    .header("Referer", "https://fanqienovel.com/")
                    .header("X-Requested-With", "XMLHttpRequest")
                    .header("Content-Type", "application/json")
                    .timeout(15000);

            // 添加token（如果有）
            if (!StrUtil.isEmpty(endpoint.getToken())) {
                request.header("token", endpoint.getToken());
            }

            // 设置POST请求体
            JSONObject jsonData = new JSONObject(data);
            request.body(jsonData.toString());

            LogUtil.d("ApiRequestUtil", "fanqie_sdk POST请求: " + finalUrl +
                    ", data: " + jsonData);

            return request.execute();

        } catch (Exception e) {
            LogUtil.e("ApiRequestUtil", "创建fanqie_sdk请求失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 为标准API创建GET请求
     * @param endpoint API端点信息
     * @param chapterId 章节ID
     * @param userAgent User-Agent字符串
     * @return HTTP响应对象，失败时返回null
     */
    public static HttpResponse makeStandardGetRequest(ApiEndpoint endpoint, String chapterId, String userAgent) {
        try {
            String url = endpoint.getUrl().replace("{chapter_id}", chapterId);

            HttpRequest request = HttpRequest.get(url)
                    .header("User-Agent", userAgent)
                    .header("Accept", "application/json, text/javascript, */*; q=0.01")
                    .header("Accept-Language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7")
                    .header("Referer", "https://fanqienovel.com/")
                    .header("X-Requested-With", "XMLHttpRequest")
                    .header("Content-Type", "application/json")
                    .timeout(15000);

            // 添加token（如果有）
            if (!StrUtil.isEmpty(endpoint.getToken())) {
                request.header("token", endpoint.getToken());
            }

            return request.execute();

        } catch (Exception e) {
            LogUtil.e("ApiRequestUtil", "创建标准GET请求失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 格式化响应时间显示，处理Double.MAX_VALUE的情况
     * @param responseTime 响应时间（秒）
     * @return 格式化后的响应时间字符串
     */
    public static String formatResponseTime(double responseTime) {
        if (responseTime == Double.MAX_VALUE) {
            return "未知";
        } else {
            return String.format("%.2fs", responseTime);
        }
    }
}
