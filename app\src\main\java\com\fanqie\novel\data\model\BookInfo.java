package com.fanqie.novel.data.model;

import java.util.List;

import cn.hutool.core.util.StrUtil;

public class BookInfo {

    private static final String TAG = "BookInfo";

    private int id;
    private String bookName;
    private List<ChapterInfo> chapterList;
    private List<String> tagList;
    private String abstractContent;
    private String author;
    private String bookId;
    private String count;
    private String imageUrl;
    private String lastUpdateChapter;
    private String lastPublishTime;

    private String chapterCount;

    private int status;
    private String statusZh;

    // 下载进度相关属性
    private boolean downloading = false;
    private int downloadProgress = 0;
    private int totalChapters = 0;
    private int downloadedChapters = 0;

    public BookInfo() {
    }

    public BookInfo(String bookName, List<ChapterInfo> chapterList,
            String tagList, String abstractContent, String author,
            String count, String imageUrl, String lastUpdateChapter, String lastPublishTime) {
        this.bookName = bookName;
        this.chapterList = chapterList;
        this.tagList = StrUtil.split(tagList, ",");
        this.abstractContent = abstractContent;
        this.author = author;
        this.count = count;
        this.imageUrl = imageUrl;
        this.lastUpdateChapter = lastUpdateChapter;
        this.lastPublishTime = lastPublishTime;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public List<String> getTagList() {
        return tagList;
    }

    public void setTagList(List<String> tagList) {
        this.tagList = tagList;
    }

    public String getTags() {
        return StrUtil.join(",", tagList);
    }

    public void setTags(String tags) {
        if (tags == null) {
            return;
        }
        this.tagList = StrUtil.split(tags, ",");
    }

    public String getAbstractContent() {
        return abstractContent;
    }

    public void setAbstractContent(String abstractContent) {
        this.abstractContent = abstractContent;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getLastUpdateChapter() {
        return lastUpdateChapter;
    }

    public void setLastUpdateChapter(String lastUpdateChapter) {
        this.lastUpdateChapter = lastUpdateChapter;
    }

    public String getLastPublishTime() {
        return lastPublishTime;
    }

    public void setLastPublishTime(String lastPublishTime) {
        this.lastPublishTime = lastPublishTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
        if (status == 1) {
            this.statusZh = "下载完成";
        } else if (status == 2) {
            this.statusZh = "已导出";
        } else {
            // 状态为0时，需要根据章节计数来确定显示文本
            // 如果有章节计数信息，说明存在部分下载失败的情况
            if (chapterCount != null && !chapterCount.isEmpty()) {
                this.statusZh = "下载失败";
            } else {
                this.statusZh = "未下载";
            }
        }
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getStatusZh() {
        return statusZh;
    }

    public void setStatusZh(String statusZh) {
        this.statusZh = statusZh;
    }

    public String getChapterCount() {
        return chapterCount;
    }

    public void setChapterCount(String chapterCount) {
        this.chapterCount = chapterCount;
        // 当设置章节计数时，重新评估状态文本
        if (status == 0) {
            if (chapterCount != null && !chapterCount.isEmpty()) {
                this.statusZh = "下载失败";
            } else {
                this.statusZh = "未下载";
            }
        }
    }

    public List<ChapterInfo> getChapterList() {
        return chapterList;
    }

    public void setChapterList(List<ChapterInfo> chapterList) {
        this.chapterList = chapterList;
    }

    // 下载进度相关方法
    public boolean isDownloading() {
        return downloading;
    }

    public void setDownloading(boolean downloading) {
        this.downloading = downloading;
    }

    public int getDownloadProgress() {
        return downloadProgress;
    }

    public void setDownloadProgress(int downloadProgress) {
        this.downloadProgress = downloadProgress;
    }

    public int getTotalChapters() {
        return totalChapters;
    }

    public void setTotalChapters(int totalChapters) {
        this.totalChapters = totalChapters;
    }

    public int getDownloadedChapters() {
        return downloadedChapters;
    }

    public void setDownloadedChapters(int downloadedChapters) {
        this.downloadedChapters = downloadedChapters;
        // 自动计算进度百分比
        if (totalChapters > 0) {
            this.downloadProgress = (int) ((downloadedChapters * 100.0f) / totalChapters);
        }
    }

    // 更新下载进度并返回是否完成
    public boolean updateDownloadProgress(int processedChapters, int totalChapters) {
        this.downloadedChapters = processedChapters;
        this.totalChapters = totalChapters;

        if (totalChapters > 0) {
            this.downloadProgress = (int) ((processedChapters * 100.0f) / totalChapters);
        }

        // 如果下载完成，更新状态
        if (processedChapters >= totalChapters && totalChapters > 0) {
            this.downloading = false;
            this.status = 1; // 设置为下载完成状态
            this.statusZh = "下载完成";
            return true; // 表示下载已完成
        }

        return false; // 下载未完成
    }
}