package com.fanqie.novel.strategy;

import android.app.Activity;

import java.util.Map;

public interface ISettingsStrategy {
    interface Callback {
        void onSuccess(Map<String, Object> config);
        void onFailure(String errorMsg);
    }
    void getConfig(Activity activity, Callback callback);
    void updateConfig(Activity activity, Map<String, Object> values, Callback callback);
    void onSuccess(Activity activity);
    void onFailure(Activity activity, String errorMsg);
} 