<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    android:stateListAnimator="@animator/card_state_list_anim">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <ImageView
            android:id="@+id/book_cover"
            android:layout_width="90dp"
            android:layout_height="130dp"
            android:scaleType="centerCrop"
            android:transitionName="cover_transition"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@color/cardview_dark_background" />

        <TextView
            android:id="@+id/book_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold"
            android:transitionName="title_transition"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/book_cover"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="书名" />

        <TextView
            android:id="@+id/book_author"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:textColor="#666666"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/book_cover"
            app:layout_constraintTop_toBottomOf="@+id/book_title"
            tools:text="作者：某某某" />

        <TextView
            android:id="@+id/book_score"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:textColor="#666666"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/book_cover"
            app:layout_constraintTop_toBottomOf="@+id/book_author"
            tools:text="分数：8.5" />

        <TextView
            android:id="@+id/book_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="#666666"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/book_cover"
            app:layout_constraintTop_toBottomOf="@+id/book_score"
            tools:text="这是一段书籍描述，介绍了书籍的内容简介，可能会很长，所以需要进行省略显示..." />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 