package com.fanqie.novel.data.db.mapper;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteConstraintException;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.util.LogUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class BookMapper extends BaseMapper {
    private static final String TAG = "BookMapper";
    private static final String TABLE_BOOKS = "books";
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_BOOK_ID = "book_id";
    private static final String COLUMN_NAME = "name";
    private static final String COLUMN_AUTHOR = "author";
    private static final String COLUMN_COVER_URL = "cover_url";
    private static final String COLUMN_DESCRIPTION = "description";
    private static final String COLUMN_CATEGORY = "category";
    private static final String COLUMN_WORD_COUNT = "word_count";
    private static final String COLUMN_LAST_CHAPTER = "last_chapter";
    private static final String COLUMN_UPDATE_TIME = "update_time";
    private static final String COLUMN_STATUS = "status";
    private static volatile BookMapper instance;

    private BookMapper(Context context) {
        super(context);
    }

    public static BookMapper getInstance(Context context) {
        if (instance == null) {
            synchronized (BookMapper.class) {
                if (instance == null) {
                    instance = new BookMapper(context.getApplicationContext());
                }
            }
        }
        return instance;
    }

    public long insertBook(BookInfo book) {
        LogUtil.d(TAG, String.format(Locale.getDefault(), "插入/更新书籍: bookId=%s, bookName=%s",
                book.getBookId(), book.getBookName()));
                
        ContentValues values = new ContentValues();
        values.put(COLUMN_BOOK_ID, book.getBookId());
        values.put(COLUMN_NAME, book.getBookName());
        values.put(COLUMN_AUTHOR, book.getAuthor());
        values.put(COLUMN_COVER_URL, book.getImageUrl());
        values.put(COLUMN_DESCRIPTION, book.getAbstractContent());
        values.put(COLUMN_CATEGORY, book.getTags());
        values.put(COLUMN_WORD_COUNT, book.getCount());
        values.put(COLUMN_LAST_CHAPTER, book.getLastUpdateChapter());
        values.put(COLUMN_UPDATE_TIME, book.getLastPublishTime());

        try {
            return db.insertOrThrow(TABLE_BOOKS, null, values);
        } catch (SQLiteConstraintException e) {
            String whereClause = COLUMN_BOOK_ID + " = ?";
            String[] whereArgs = {book.getBookId()};
            return db.update(TABLE_BOOKS, values, whereClause, whereArgs);
        }
    }

    public List<BookInfo> getBookList(String bookId) {
        String selection = null;
        String[] selectionArgs = null;

        if (bookId != null && !bookId.isEmpty()) {
            selection = COLUMN_BOOK_ID + " = ?";
            selectionArgs = new String[]{bookId};
        }

        List<BookInfo> bookList = new ArrayList<>();
        try (Cursor cursor = db.query(TABLE_BOOKS, null, selection, selectionArgs,
                null, null, "id DESC")) {
            while (cursor.moveToNext()) {
                BookInfo book = new BookInfo();
                book.setId(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_ID)));
                book.setBookId(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_BOOK_ID)));
                book.setBookName(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_NAME)));
                book.setAuthor(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_AUTHOR)));
                book.setImageUrl(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_COVER_URL)));
                book.setAbstractContent(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_DESCRIPTION)));
                book.setTags(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CATEGORY)));
                book.setCount(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_WORD_COUNT)));
                book.setLastUpdateChapter(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_LAST_CHAPTER)));
                book.setLastPublishTime(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_UPDATE_TIME)));
                book.setStatus(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_STATUS)));
                bookList.add(book);
            }
        }
        return bookList;
    }

    public void deleteBookByBookId(String bookId) {
        String whereClause = COLUMN_BOOK_ID + " = ?";
        String[] whereArgs = {bookId};
        db.delete(TABLE_BOOKS, whereClause, whereArgs);
    }

    public int updateStatusByBookId(String bookId, int status) {
        ContentValues values = new ContentValues();
        values.put(COLUMN_STATUS, status);
        String whereClause = COLUMN_BOOK_ID + " = ?";
        String[] whereArgs = {bookId};
        return db.update(TABLE_BOOKS, values, whereClause, whereArgs);
    }
} 