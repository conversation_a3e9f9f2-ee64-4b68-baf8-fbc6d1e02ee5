package com.fanqie.novel.data.db.mapper;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteConstraintException;

import com.fanqie.novel.data.model.ChapterDownloadStats;
import com.fanqie.novel.data.model.ChapterInfo;
import com.fanqie.novel.util.LogUtil;

import java.util.ArrayList;
import java.util.List;

public class ChapterMapper extends BaseMapper {
    private static final String TABLE_CHAPTER_INFO = "chapter_info";
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_BOOK_ID = "book_id";
    private static final String COLUMN_CHAPTER_ID = "chapter_id";
    private static final String COLUMN_CHAPTER_NAME = "chapter_name";
    private static final String COLUMN_CONTENT = "content";
    private static final String COLUMN_SORT = "sort";
    private static final String COLUMN_COUNT = "count";
    private static final String COLUMN_STATUS = "status";
    private static volatile ChapterMapper instance;
    private static final String TAG = "ChapterMapper";

    private ChapterMapper(Context context) {
        super(context);
    }

    public static ChapterMapper getInstance(Context context) {
        if (instance == null) {
            synchronized (ChapterMapper.class) {
                if (instance == null) {
                    instance = new ChapterMapper(context.getApplicationContext());
                }
            }
        }
        return instance;
    }

    public long insertChapter(ChapterInfo chapter) {
        ContentValues values = new ContentValues();
        values.put(COLUMN_BOOK_ID, chapter.getBookId());
        values.put(COLUMN_CHAPTER_ID, chapter.getId());
        values.put(COLUMN_CHAPTER_NAME, chapter.getTitle());
        values.put(COLUMN_CONTENT, chapter.getContent());
        values.put(COLUMN_SORT, chapter.getSort());
        values.put(COLUMN_COUNT, chapter.getCount());
        values.put(COLUMN_STATUS, chapter.getStatus());

        try {
            return db.insertOrThrow(TABLE_CHAPTER_INFO, null, values);
        } catch (SQLiteConstraintException e) {
            String whereClause = COLUMN_CHAPTER_ID + " = ?";
            String[] whereArgs = { chapter.getId() };
            return db.update(TABLE_CHAPTER_INFO, values, whereClause, whereArgs);
        }
    }

    public void insertChapters(List<ChapterInfo> chapters) {
        for (ChapterInfo chapter : chapters) {
            try {
                insertChapter(chapter);
            } catch (Exception e) {
                LogUtil.e(TAG, "插入章节失败: " + chapter.getTitle(), e);
            }
        }
    }

    public List<ChapterInfo> getChaptersByBookId(String bookId) {
        String selection = COLUMN_BOOK_ID + " = ?";
        String[] selectionArgs = { bookId };

        List<ChapterInfo> chapters = new ArrayList<>();
        try (Cursor cursor = db.query(TABLE_CHAPTER_INFO, null, selection, selectionArgs,
                null, null, COLUMN_SORT)) {
            while (cursor.moveToNext()) {
                ChapterInfo chapter = new ChapterInfo();
                chapter.setId(String.valueOf(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_ID))));
                chapter.setBookId(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_BOOK_ID)));
                chapter.setTitle(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CHAPTER_NAME)));
                chapter.setContent(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CONTENT)));
                chapter.setSort(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_SORT)));
                chapter.setCount(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_COUNT)));
                chapter.setStatus(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_STATUS)));
                chapters.add(chapter);
            }
        }
        return chapters;
    }

    public void deleteChaptersByBookId(String bookId) {
        String whereClause = COLUMN_BOOK_ID + " = ?";
        String[] whereArgs = { bookId };
        db.delete(TABLE_CHAPTER_INFO, whereClause, whereArgs);
    }

    public String countChapters(String bookId) {
        String query = "SELECT COUNT(*) AS total, " +
                "SUM(CASE WHEN " + COLUMN_COUNT + " > 500 THEN 1 ELSE 0 END) AS count_valid " +
                "FROM " + TABLE_CHAPTER_INFO +
                " WHERE " + COLUMN_BOOK_ID + " = ?";

        try (Cursor cursor = db.rawQuery(query, new String[] { bookId })) {
            if (cursor.moveToFirst()) {
                int total = cursor.getInt(cursor.getColumnIndexOrThrow("total"));
                int countValid = cursor.getInt(cursor.getColumnIndexOrThrow("count_valid"));

                // 如果所有章节都下载成功（内容长度>500），返回null（不显示计数）
                // 如果存在下载失败的章节，返回"成功数/总数"格式
                if (total > 0 && countValid < total) {
                    return countValid + "/" + total;
                }
            }
        }
        return null;
    }

    /**
     * 删除下载失败的章节
     */
    public void deleteFailedChapters(String bookId) {
        String whereClause = COLUMN_BOOK_ID + " = ? AND " + COLUMN_STATUS + " = 0";
        String[] whereArgs = { bookId };

        // 先记录要删除的章节
        try (Cursor cursor = db.query(TABLE_CHAPTER_INFO,
                new String[] { COLUMN_CHAPTER_ID },
                whereClause,
                whereArgs,
                null, null, null)) {
            cursor.getCount();
        }

        db.delete(TABLE_CHAPTER_INFO, whereClause, whereArgs);
    }

    /**
     * 获取已成功下载的章节ID列表
     */
    public List<String> getSuccessChapterIds(String bookId) {
        String[] columns = { COLUMN_CHAPTER_ID };
        String selection = COLUMN_BOOK_ID + " = ? AND " + COLUMN_STATUS + " = 1";
        String[] selectionArgs = { bookId };

        List<String> chapterIds = new ArrayList<>();
        try (Cursor cursor = db.query(TABLE_CHAPTER_INFO, columns, selection, selectionArgs,
                null, null, null)) {
            while (cursor.moveToNext()) {
                chapterIds.add(cursor.getString(0));
            }
        }
        return chapterIds;
    }

    /**
     * 获取已下载的章节ID列表（包括成功和失败的章节）
     */
    public List<String> getDownloadedChapterIds(String bookId) {
        String[] columns = { COLUMN_CHAPTER_ID };
        String selection = COLUMN_BOOK_ID + " = ?";
        String[] selectionArgs = { bookId };

        List<String> chapterIds = new ArrayList<>();
        try (Cursor cursor = db.query(TABLE_CHAPTER_INFO, columns, selection, selectionArgs,
                null, null, null)) {
            while (cursor.moveToNext()) {
                chapterIds.add(cursor.getString(0));
            }
        }
        return chapterIds;
    }

    /**
     * 获取有效下载的章节ID列表（内容长度>500字符）
     */
    public List<String> getValidDownloadedChapterIds(String bookId) {
        String[] columns = { COLUMN_CHAPTER_ID };
        String selection = COLUMN_BOOK_ID + " = ? AND " + COLUMN_COUNT + " > 500";
        String[] selectionArgs = { bookId };

        List<String> chapterIds = new ArrayList<>();
        try (Cursor cursor = db.query(TABLE_CHAPTER_INFO, columns, selection, selectionArgs,
                null, null, null)) {
            while (cursor.moveToNext()) {
                chapterIds.add(cursor.getString(0));
            }
        }
        return chapterIds;
    }

    /**
     * 获取章节下载统计信息
     * 
     * @param bookId 书籍ID
     * @return 包含总数、有效下载数、失败数的统计信息
     */
    public ChapterDownloadStats getChapterDownloadStats(String bookId) {
        String query = "SELECT COUNT(*) AS total, " +
                "SUM(CASE WHEN " + COLUMN_COUNT + " > 500 THEN 1 ELSE 0 END) AS valid_count, " +
                "SUM(CASE WHEN " + COLUMN_COUNT + " <= 500 OR " + COLUMN_COUNT
                + " IS NULL THEN 1 ELSE 0 END) AS failed_count " +
                "FROM " + TABLE_CHAPTER_INFO +
                " WHERE " + COLUMN_BOOK_ID + " = ?";

        try (Cursor cursor = db.rawQuery(query, new String[] { bookId })) {
            if (cursor.moveToFirst()) {
                int total = cursor.getInt(cursor.getColumnIndexOrThrow("total"));
                int validCount = cursor.getInt(cursor.getColumnIndexOrThrow("valid_count"));
                int failedCount = cursor.getInt(cursor.getColumnIndexOrThrow("failed_count"));

                return new ChapterDownloadStats(total, validCount, failedCount);
            }
        }
        return new ChapterDownloadStats(0, 0, 0);
    }

}