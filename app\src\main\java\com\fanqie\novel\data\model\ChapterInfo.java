package com.fanqie.novel.data.model;

public class ChapterInfo {
    private String id;
    private String bookId;
    private String content;
    private int sort;
    private int count;
    private int status; // 0:下载失败 1:下载成功

    private String title;

    private String link;

    public ChapterInfo() {
    }

    public ChapterInfo(String title, String link, int sort) {
        this.title = title;
        this.link = link;
        this.sort = sort;
        if (link != null) {
            this.id = link.substring(link.lastIndexOf("/") + 1);
        }
    }

    // Getter methods
    public String getId() {
        return id;
    }

    public String getIdString() {
        return id != null ? id : String.valueOf(this.id);
    }

    // Setter methods
    public void setId(String id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
        if (link != null) {
            this.id = link.substring(link.lastIndexOf("/") + 1);
        }
    }
}