package com.fanqie.novel.util;


import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;

public class ContentCleaner {
    private static final Pattern HEADER_FOOTER_PATTERN = Pattern.compile("<header>.*?</header>", Pattern.DOTALL);
    private static final Pattern ARTICLE_TAG_PATTERN = Pattern.compile("</?article>");
    private static final Pattern P_TAG_PATTERN = Pattern.compile("</?p>");
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");
    private static final Pattern UNICODE_ANGLE_PATTERN = Pattern.compile("\\\\u003c|\\\\u003e");
    private static final Pattern MULTI_NEWLINE_PATTERN = Pattern.compile("\n{2,}");

    public static String processContent(String content) {
        try {
            // HTML清理逻辑
            content = HEADER_FOOTER_PATTERN.matcher(content).replaceAll("");
            content = ARTICLE_TAG_PATTERN.matcher(content).replaceAll("");
            content = P_TAG_PATTERN.matcher(content).replaceAll("\n"); // 替换 <p> 和 </p> 为换行符
            content = HTML_TAG_PATTERN.matcher(content).replaceAll(""); // 移除其他 HTML 标签
            content = UNICODE_ANGLE_PATTERN.matcher(content).replaceAll("");

            // 格式化处理
            content = MULTI_NEWLINE_PATTERN.matcher(content).replaceAll("\n").trim();

            return content;
        } catch (Exception e) {
            throw new RuntimeException("解析章节内容失败", e);
        }
    }


    // 章节内容处理，移除HTML标签、格式化段落等
    public static String processChapterContent(String content) {
        if (StrUtil.isBlank(content))
            return "";
        try {
            content = ReUtil.replaceAll(content, "<header>.*?</header>", "");
            content = ReUtil.replaceAll(content, "<footer>.*?</footer>", "");
            content = ReUtil.replaceAll(content, "</?article>", "");
            content = ReUtil.replaceAll(content, "<p[^>]*>", "\n    ");
            content = ReUtil.replaceAll(content, "</p>", "");
            content = ReUtil.replaceAll(content, "<[^>]+>", "");
            content = ReUtil.replaceAll(content, "\\u003c|\\u003e", "");
            content = ReUtil.replaceAll(content, "\n{3,}", "\n\n").trim();
            List<String> lines = Arrays.stream(content.split("\n")).map(String::trim).filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            return String.join("\n", lines);
        } catch (Exception e) {
            LogUtil.e("PythonLikeDownloadStrategy", "内容处理错误: " + e.getMessage());
            return content;
        }
    }

}
