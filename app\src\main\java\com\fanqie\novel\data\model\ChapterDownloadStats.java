package com.fanqie.novel.data.model;

/**
 * 章节下载统计信息类
 * 
 * 用于封装章节下载的统计数据，包括总章节数、有效下载数、失败下载数等信息。
 * 提供便捷的方法来判断下载状态和获取进度信息。
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
public class ChapterDownloadStats {
    
    /** 总章节数 */
    public final int totalChapters;
    
    /** 有效下载数（内容长度>500字符的章节） */
    public final int validDownloads;
    
    /** 失败下载数（内容长度≤500字符或为空的章节） */
    public final int failedDownloads;

    /**
     * 构造函数
     * 
     * @param totalChapters 总章节数
     * @param validDownloads 有效下载数
     * @param failedDownloads 失败下载数
     */
    public ChapterDownloadStats(int totalChapters, int validDownloads, int failedDownloads) {
        this.totalChapters = totalChapters;
        this.validDownloads = validDownloads;
        this.failedDownloads = failedDownloads;
    }

    /**
     * 判断是否所有章节都下载成功
     * 
     * @return 如果所有章节都下载成功返回true，否则返回false
     */
    public boolean isAllDownloaded() {
        return totalChapters > 0 && validDownloads == totalChapters;
    }

    /**
     * 判断是否存在下载失败的章节
     * 
     * @return 如果存在下载失败的章节返回true，否则返回false
     */
    public boolean hasFailedChapters() {
        return failedDownloads > 0;
    }

    /**
     * 获取下载进度百分比
     * 
     * @return 下载进度百分比（0-100）
     */
    public int getProgressPercentage() {
        if (totalChapters == 0)
            return 0;
        return (int) ((validDownloads * 100.0f) / totalChapters);
    }

    /**
     * 获取进度显示字符串
     * 
     * @return 格式为"成功数/总数"的字符串，例如"15/20"
     */
    public String getProgressString() {
        return validDownloads + "/" + totalChapters;
    }

    /**
     * 获取详细的统计信息字符串
     * 
     * @return 包含所有统计信息的字符串
     */
    public String getDetailedStats() {
        return String.format("总章节: %d, 成功: %d, 失败: %d, 进度: %d%%",
                totalChapters, validDownloads, failedDownloads, getProgressPercentage());
    }

    /**
     * 判断是否有章节需要下载
     * 
     * @return 如果还有章节需要下载返回true，否则返回false
     */
    public boolean hasChaptersToDownload() {
        return validDownloads < totalChapters;
    }

    /**
     * 获取剩余需要下载的章节数
     * 
     * @return 剩余需要下载的章节数
     */
    public int getRemainingChapters() {
        return Math.max(0, totalChapters - validDownloads);
    }

    @Override
    public String toString() {
        return "ChapterDownloadStats{" +
                "totalChapters=" + totalChapters +
                ", validDownloads=" + validDownloads +
                ", failedDownloads=" + failedDownloads +
                ", progressPercentage=" + getProgressPercentage() + "%" +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ChapterDownloadStats that = (ChapterDownloadStats) obj;
        return totalChapters == that.totalChapters &&
                validDownloads == that.validDownloads &&
                failedDownloads == that.failedDownloads;
    }

    @Override
    public int hashCode() {
        int result = totalChapters;
        result = 31 * result + validDownloads;
        result = 31 * result + failedDownloads;
        return result;
    }
}
