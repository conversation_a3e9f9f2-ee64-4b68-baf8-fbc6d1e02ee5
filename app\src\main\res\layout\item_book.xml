<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="8dp"
    android:layout_marginHorizontal="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    app:cardBackgroundColor="#FFFFFF">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="15dp">

        <!-- 书籍信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 书籍封面 -->
            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">
                
                <ImageView
                    android:id="@+id/book_cover"
                    android:layout_width="90dp"
                    android:layout_height="145dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/ic_bookshelf"
                    android:contentDescription="书籍封面" />
            </androidx.cardview.widget.CardView>

            <!-- 书籍详情 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/book_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"
                    tools:text="书籍标题" />

                <TextView
                    android:id="@+id/book_author"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="#666666"
                    android:textSize="13sp"
                    tools:text="作者：某某作者" />

                <TextView
                    android:id="@+id/book_word_count"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="#666666"
                    android:textSize="13sp"
                    tools:text="字数：100万字" />

                <TextView
                    android:id="@+id/book_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="#666666"
                    android:textSize="13sp"
                    tools:text="状态：已完结 10/10" />

                <TextView
                    android:id="@+id/book_last_chapter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="#666666"
                    android:textSize="13sp"
                    tools:text="最新章节：第100章 完结篇" />

            </LinearLayout>
        </LinearLayout>

        <!-- 下载进度条区域 -->
        <LinearLayout
            android:id="@+id/download_progress_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="10dp"
            android:visibility="gone">

            <TextView
                android:id="@+id/download_progress_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="#666666"
                android:text="下载进度: 0%"
                android:gravity="end"
                android:layout_marginBottom="4dp"/>

            <ProgressBar
                android:id="@+id/download_progress_bar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:max="100"
                android:progress="0"
                android:progressTint="@color/purple_66b"/>
        </LinearLayout>

        <!-- 按钮区域 -->
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal">

            <Button
                    android:id="@+id/btn_download"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:backgroundTint="#1890FF"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:text="下载"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

            <Button
                    android:id="@+id/btn_export"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:backgroundTint="#1890FF"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:text="导出"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

            <Button
                    android:id="@+id/btn_delete"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:backgroundTint="#1890FF"
                    android:text="删除"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView> 