# 番茄小说下载器

一个专为安卓平台开发的小说阅读与下载应用，支持番茄小说的搜索、下载、阅读与本地管理。项目采用原生 Java 开发，界面简洁，功能完善，适合 Android 5.0 及以上设备。

## 主要特性

- 小说名称搜索，展示封面、作者、字数等信息
- 书籍详情页，支持章节目录浏览、简介展开/收起
- 支持单章节或整本下载，下载进度可视化
- 本地阅读，支持多种主题、字体大小调节、翻页效果
- 支持TXT格式导出，便于离线保存
- 书架管理，快速访问已下载/收藏书籍
- 多线程下载与断点续传
- 记忆阅读进度与偏好设置

## 项目结构

```text
app/
├── src/
│   ├── main/
│   │   ├── java/com/fanqie/novel/
│   │   │   ├── adapter/      # 各类适配器（如书架、章节、搜索结果等）
│   │   │   ├── constants/    # 常量定义
│   │   │   ├── data/         # 数据层，含db/mapper/数据模型
│   │   │   ├── domain/       # 领域模型与核心业务对象
│   │   │   ├── service/      # 后台服务（如下载管理）
│   │   │   ├── ui/           # Activity/Fragment及界面相关
│   │   │   └── util/         # 工具类（下载、导出、加密、日志等）
│   │   └── res/              # 资源文件（布局、图片、动画、主题等）
```

## 技术栈与依赖

- 语言：Java（原生 Android 开发）
- 最低SDK：24（Android 7.0）
- 目标SDK：33
- 构建工具：Gradle
- 主要依赖：
  - AndroidX、Material Components
  - Picasso（图片加载）
  - Hutool（网络/工具）
  - Fastjson（JSON 解析）
  - Jsoup（HTML 解析）
- 数据存储：SQLite（本地数据库）
- 多线程与下载管理：自定义实现

## 构建与运行

1. 安装 [Android Studio](https://developer.android.com/studio)
2. 配置 JDK 17 与 Android SDK 33+
3. 克隆本项目并用 Android Studio 打开
4. 连接设备或启动模拟器
5. 点击"Run"或使用 Gradle 构建 APK：
   ```sh
   ./gradlew assembleDebug
   ```
6. 安装 APK 至设备，首次运行需授予存储和网络权限

## 权限说明

- 网络权限：用于访问和下载小说内容
- 存储权限：用于保存下载的小说和导出TXT文件

## 使用说明

1. **搜索小说**：在首页输入小说名称，点击搜索，浏览结果
2. **查看详情**：点击书籍进入详情页，浏览简介与章节目录
3. **下载小说**：可选择单章或全本下载，进度实时显示
4. **本地阅读**：支持多主题、字体调节、翻页动画，自动记忆进度
5. **导出TXT**：下载完成后可一键导出为TXT文件
6. **书架管理**：已下载/收藏书籍可在书架快速访问

## 贡献指南

欢迎提交 Issue 或 Pull Request 参与项目改进。建议先在本地充分测试后再提交。

## License

本项目仅供学习与交流，禁止用于商业用途。如需转载或引用，请注明出处。