package com.fanqie.novel.constants;

import android.util.Log;
import cn.hutool.core.convert.Convert;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 全局缓存管理类
 */
public class CommonCache {
    private static final String TAG = "CommonCache";

    // 使用 ConcurrentHashMap 保证线程安全
    private static final Map<String, Map<String, String>> BOOK_CACHE = new ConcurrentHashMap<>();
    private static final Map<String, Integer> NOTIFICATION_ID_CACHE = new ConcurrentHashMap<>();
    private static final Map<String, String> BOOK_NAME_CACHE = new ConcurrentHashMap<>();

    private CommonCache() {
        // 私有构造函数防止实例化
    }

    /**
     * 获取书籍的章节内容缓存
     */
    public static Map<String, String> getChapterDict(String bookId) {
        return BOOK_CACHE.computeIfAbsent(bookId, k -> new ConcurrentHashMap<>());
    }

    /**
     * 清除指定书籍的章节缓存
     */
    public static void clearChapterCache(String bookId) {
        BOOK_CACHE.remove(bookId);
        Log.d(TAG, "Cleared chapter cache for book: " + bookId);
    }

    /**
     * 初始化通知ID
     */
    public static void initBookNotificationId(String bookId) {
        Integer notificationId = Convert.toInt(System.currentTimeMillis() / 1000);
        NOTIFICATION_ID_CACHE.put(bookId, notificationId);
        Log.d(TAG, "Initialized notification ID for book: " + bookId + ", ID: " + notificationId);
    }

    /**
     * 获取通知ID
     */
    public static int getBookNotificationId(String bookId) {
        return NOTIFICATION_ID_CACHE.computeIfAbsent(bookId,
                k -> Convert.toInt(System.currentTimeMillis() / 1000));
    }

    /**
     * 缓存书籍名称
     */
    public static void putBookName(String bookId, String bookName) {
        if (bookId != null && bookName != null) {
            BOOK_NAME_CACHE.put(bookId, bookName);
        }
    }

    /**
     * 获取书籍名称
     */
    public static String getBookName(String bookId) {
        return BOOK_NAME_CACHE.computeIfAbsent(bookId, k -> "正在下载");
    }

    /**
     * 清除指定书籍的所有缓存
     */
    public static void clearBookCache(String bookId) {
        BOOK_CACHE.remove(bookId);
        NOTIFICATION_ID_CACHE.remove(bookId);
        BOOK_NAME_CACHE.remove(bookId);
    }

    /**
     * 清除所有缓存
     */
    public static void clearAllCache() {
        BOOK_CACHE.clear();
        NOTIFICATION_ID_CACHE.clear();
        BOOK_NAME_CACHE.clear();
    }

}
