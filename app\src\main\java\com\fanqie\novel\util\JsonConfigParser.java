package com.fanqie.novel.util;

import android.util.Log;

import com.fanqie.novel.data.model.ApiEndpoint;
import com.fanqie.novel.domain.BatchConfig;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JSON配置解析器
 * 负责解析用户输入的JSON配置，并转换为应用内部的配置数据结构
 */
public class JsonConfigParser {
    private static final String TAG = "JsonConfigParser";

    /**
     * JSON配置解析结果
     */
    public static class ParseResult {
        public boolean success;
        public String errorMessage;
        public List<ApiEndpoint> apiEndpoints;
        public BatchConfig batchConfig;
        public Map<String, Object> basicConfig;

        public ParseResult() {
            this.apiEndpoints = new ArrayList<>();
            this.basicConfig = new HashMap<>();
        }
    }

    /**
     * 解析JSON配置字符串
     * 
     * @param jsonConfig JSON配置字符串
     * @return 解析结果
     */
    public static ParseResult parseJsonConfig(String jsonConfig) {
        ParseResult result = new ParseResult();

        if (jsonConfig == null || jsonConfig.trim().isEmpty()) {
            result.success = false;
            result.errorMessage = "JSON配置不能为空";
            return result;
        }

        try {
            JSONObject json = new JSONObject(jsonConfig);

            // 解析基础配置
            parseBasicConfig(json, result);

            // 解析API端点配置
            parseApiEndpoints(json, result);

            // 解析批量配置
            parseBatchConfig(json, result);

            result.success = true;
            Log.i(TAG, "JSON配置解析成功");

        } catch (JSONException e) {
            result.success = false;
            result.errorMessage = "JSON格式错误: " + e.getMessage();
            Log.e(TAG, "JSON配置解析失败", e);
        } catch (Exception e) {
            result.success = false;
            result.errorMessage = "配置解析异常: " + e.getMessage();
            Log.e(TAG, "配置解析异常", e);
        }

        return result;
    }

    /**
     * 解析基础配置参数
     */
    private static void parseBasicConfig(JSONObject json, ParseResult result) throws JSONException {
        // 解析基础参数
        if (json.has("max_workers")) {
            result.basicConfig.put("max_workers", json.getInt("max_workers"));
        }
        if (json.has("max_retries")) {
            result.basicConfig.put("max_retries", json.getInt("max_retries"));
        }
        if (json.has("request_timeout")) {
            result.basicConfig.put("request_timeout", json.getInt("request_timeout"));
        }
        if (json.has("status_file")) {
            result.basicConfig.put("status_file", json.getString("status_file"));
        }
        if (json.has("request_rate_limit")) {
            result.basicConfig.put("request_rate_limit", json.getDouble("request_rate_limit"));
        }
        if (json.has("auth_token")) {
            result.basicConfig.put("auth_token", json.getString("auth_token"));
        }
        if (json.has("server_url")) {
            result.basicConfig.put("server_url", json.getString("server_url"));
        }
    }

    /**
     * 解析API端点配置
     */
    private static void parseApiEndpoints(JSONObject json, ParseResult result) throws JSONException {
        if (!json.has("api_endpoints")) {
            return;
        }

        JSONArray endpoints = json.getJSONArray("api_endpoints");
        for (int i = 0; i < endpoints.length(); i++) {
            JSONObject endpoint = endpoints.getJSONObject(i);

            String url = endpoint.getString("url");
            String name = endpoint.getString("name");
            String token = endpoint.optString("token", null);

            // 解析URL参数
            Map<String, Object> params = new HashMap<>();
            if (endpoint.has("params")) {
                JSONObject paramsObj = endpoint.getJSONObject("params");
                params = jsonObjectToMap(paramsObj);
            }

            // 解析POST数据
            Map<String, Object> data = new HashMap<>();
            if (endpoint.has("data")) {
                JSONObject dataObj = endpoint.getJSONObject("data");
                data = jsonObjectToMap(dataObj);
            }

            ApiEndpoint apiEndpoint = new ApiEndpoint(url, name, token, params, data);
            result.apiEndpoints.add(apiEndpoint);
        }
    }

    /**
     * 解析批量配置
     */
    private static void parseBatchConfig(JSONObject json, ParseResult result) throws JSONException {
        if (!json.has("batch_config")) {
            return;
        }

        JSONObject batchConfigJson = json.getJSONObject("batch_config");

        String name = batchConfigJson.optString("name", "");
        String baseUrl = batchConfigJson.optString("base_url", "");
        String batchEndpoint = batchConfigJson.optString("batch_endpoint", "");
        String token = batchConfigJson.optString("token", "");
        int maxBatchSize = batchConfigJson.optInt("max_batch_size", 10);
        int timeout = batchConfigJson.optInt("timeout", 10);
        boolean enabled = batchConfigJson.optBoolean("enabled", false);

        result.batchConfig = new BatchConfig(baseUrl, batchEndpoint, token, maxBatchSize, timeout, enabled);

        // 将批量配置的name也添加到基础配置中，保持兼容性
        result.basicConfig.put("name", name);
        result.basicConfig.put("max_batch_size", maxBatchSize);
    }

    /**
     * 将JSONObject转换为Map
     */
    private static Map<String, Object> jsonObjectToMap(JSONObject jsonObject) throws JSONException {
        Map<String, Object> map = new HashMap<>();

        for (java.util.Iterator<String> keys = jsonObject.keys(); keys.hasNext();) {
            String key = keys.next();
            Object value = jsonObject.get(key);

            if (value instanceof JSONObject) {
                map.put(key, jsonObjectToMap((JSONObject) value));
            } else if (value instanceof JSONArray) {
                // 简单处理JSONArray，转换为List
                JSONArray array = (JSONArray) value;
                List<Object> list = new ArrayList<>();
                for (int i = 0; i < array.length(); i++) {
                    list.add(array.get(i));
                }
                map.put(key, list);
            } else {
                map.put(key, value);
            }
        }

        return map;
    }

    /**
     * 验证JSON配置的完整性
     */
    public static boolean validateJsonConfig(String jsonConfig) {
        try {
            if (jsonConfig == null || jsonConfig.trim().isEmpty()) {
                Log.w(TAG, "JSON配置为空或null");
                return false;
            }

            JSONObject json = new JSONObject(jsonConfig);

            // 检查必要字段
            if (!json.has("api_endpoints")) {
                Log.w(TAG, "JSON配置缺少api_endpoints字段");
                return false;
            }

            JSONArray endpoints = json.getJSONArray("api_endpoints");
            if (endpoints.length() == 0) {
                Log.w(TAG, "api_endpoints数组为空");
                return false;
            }

            // 验证每个端点的必要字段
            for (int i = 0; i < endpoints.length(); i++) {
                JSONObject endpoint = endpoints.getJSONObject(i);
                if (!endpoint.has("url") || !endpoint.has("name")) {
                    Log.w(TAG, "第" + (i + 1) + "个API端点缺少必要字段: url或name");
                    return false;
                }

                // 验证URL格式
                String url = endpoint.getString("url");
                if (url.trim().isEmpty()) {
                    Log.w(TAG, "第" + (i + 1) + "个API端点的URL为空");
                    return false;
                }

                // 验证name格式
                String name = endpoint.getString("name");
                if (name.trim().isEmpty()) {
                    Log.w(TAG, "第" + (i + 1) + "个API端点的name为空");
                    return false;
                }
            }

            // 验证批量配置（如果存在）
            if (json.has("batch_config")) {
                JSONObject batchConfig = json.getJSONObject("batch_config");
                if (batchConfig.has("enabled") && batchConfig.getBoolean("enabled")) {
                    if (!batchConfig.has("base_url") || batchConfig.getString("base_url").trim().isEmpty()) {
                        Log.w(TAG, "批量配置启用但base_url为空");
                        return false;
                    }
                }
            }

            Log.i(TAG, "JSON配置验证通过，包含 " + endpoints.length() + " 个API端点");
            return true;

        } catch (JSONException e) {
            Log.e(TAG, "JSON格式验证失败: " + e.getMessage(), e);
            return false;
        } catch (Exception e) {
            Log.e(TAG, "JSON配置验证异常: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 将解析结果转换为兼容现有系统的JSONObject格式
     */
    public static JSONObject convertToCompatibleConfig(ParseResult parseResult) throws JSONException {
        JSONObject config = new JSONObject();

        // 添加基础配置
        for (Map.Entry<String, Object> entry : parseResult.basicConfig.entrySet()) {
            config.put(entry.getKey(), entry.getValue());
        }

        return config;
    }

    /**
     * 获取详细的错误信息
     */
    public static String getDetailedErrorMessage(String jsonConfig) {
        if (jsonConfig == null || jsonConfig.trim().isEmpty()) {
            return "JSON配置为空，请输入有效的JSON配置内容";
        }

        try {
            JSONObject json = new JSONObject(jsonConfig);

            if (!json.has("api_endpoints")) {
                return "JSON配置缺少必要的 'api_endpoints' 字段，请参考示例配置";
            }

            JSONArray endpoints = json.getJSONArray("api_endpoints");
            if (endpoints.length() == 0) {
                return "api_endpoints 数组为空，至少需要配置一个API端点";
            }

            // 检查每个端点
            for (int i = 0; i < endpoints.length(); i++) {
                JSONObject endpoint = endpoints.getJSONObject(i);
                String prefix = "第" + (i + 1) + "个API端点: ";

                if (!endpoint.has("url")) {
                    return prefix + "缺少 'url' 字段";
                }
                if (!endpoint.has("name")) {
                    return prefix + "缺少 'name' 字段";
                }

                String url = endpoint.getString("url");
                if (url.trim().isEmpty()) {
                    return prefix + "url 字段不能为空";
                }

                String name = endpoint.getString("name");
                if (name.trim().isEmpty()) {
                    return prefix + "name 字段不能为空";
                }
            }

            return "JSON配置格式正确";

        } catch (JSONException e) {
            return "JSON格式错误: " + e.getMessage() + "。请检查JSON语法是否正确";
        } catch (Exception e) {
            return "配置解析异常: " + e.getMessage();
        }
    }

    /**
     * 生成配置统计信息
     */
    public static String getConfigStatistics(ParseResult result) {
        if (!result.success) {
            return "配置解析失败: " + result.errorMessage;
        }

        StringBuilder stats = new StringBuilder();
        stats.append("配置统计信息:\n");
        stats.append("• API端点数量: ").append(result.apiEndpoints.size()).append("\n");

        if (!result.basicConfig.isEmpty()) {
            stats.append("• 基础配置项: ").append(result.basicConfig.size()).append("\n");
        }

        if (result.batchConfig != null) {
            stats.append("• 批量下载: ").append(result.batchConfig.isEnabled() ? "启用" : "禁用").append("\n");
            if (result.batchConfig.isEnabled()) {
                stats.append("• 批次大小: ").append(result.batchConfig.getMaxBatchSize()).append("\n");
            }
        }

        return stats.toString();
    }
}
