package com.fanqie.novel.data.db;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

public class DatabaseHelper extends SQLiteOpenHelper {
    private static final String TAG = "DatabaseHelper";
    private static final String DATABASE_NAME = "novel.db";
    private static final int DATABASE_VERSION = 4;
    private static DatabaseHelper instance;

    private DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    public static synchronized DatabaseHelper getInstance(Context context) {
        if (instance == null) {
            instance = new DatabaseHelper(context.getApplicationContext());
        }
        return instance;
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        // 创建章节表
        db.execSQL("CREATE TABLE IF NOT EXISTS chapter_info (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "book_id TEXT," +
                "chapter_id TEXT NOT NULL UNIQUE," +
                "chapter_name TEXT," +
                "content TEXT," +
                "sort INTEGER," +
                "count INTEGER," +
                "status INTEGER DEFAULT 0)");

        // 创建书籍表
        db.execSQL("CREATE TABLE IF NOT EXISTS books (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "book_id TEXT UNIQUE NOT NULL," +
                "name TEXT," +
                "author TEXT," +
                "cover_url TEXT," +
                "description TEXT," +
                "category TEXT," +
                "word_count INTEGER," +
                "last_chapter TEXT," +
                "update_time TEXT," +
                "source TEXT," +
                "status INTEGER DEFAULT 0)");

        // 创建SVIP Cookie表
        db.execSQL("CREATE TABLE IF NOT EXISTS svip_cookie (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "svip_cookie TEXT)");

        db.execSQL("CREATE TABLE tomato_variable (" +
                "    id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "    install_id TEXT," +
                "    server_device_id TEXT," +
                "    aid TEXT," +
                "    update_version_code TEXT" +
                ");\n");

        db.execSQL("CREATE TABLE IF NOT EXISTS download_config (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "strategy TEXT," +
                "config_json TEXT," +
                "is_current INTEGER DEFAULT 0" +
                ")");
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS chapter_info");
        db.execSQL("DROP TABLE IF EXISTS books");
        db.execSQL("DROP TABLE IF EXISTS svip_cookie");
        db.execSQL("DROP TABLE IF EXISTS tomato_variable");
        db.execSQL("DROP TABLE IF EXISTS download_config");
        onCreate(db);
    }
}