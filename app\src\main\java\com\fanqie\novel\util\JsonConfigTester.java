package com.fanqie.novel.util;

import android.content.Context;
import android.util.Log;

import com.fanqie.novel.constants.DownloadStrategyType;
import com.fanqie.novel.data.model.ApiEndpoint;
import com.fanqie.novel.domain.BatchConfig;

import org.json.JSONObject;

import java.util.List;

/**
 * JSON配置功能测试工具类
 * 用于验证JSON配置系统的各项功能
 */
public class JsonConfigTester {
    private static final String TAG = "JsonConfigTester";

    /**
     * 测试结果类
     */
    public static class TestResult {
        public boolean success;
        public String message;
        public String details;

        public TestResult(boolean success, String message, String details) {
            this.success = success;
            this.message = message;
            this.details = details;
        }
    }

    /**
     * 全面测试JSON配置功能
     */
    public static TestResult runFullTest(Context context) {
        StringBuilder details = new StringBuilder();
        boolean allTestsPassed = true;

        Log.i(TAG, "开始JSON配置功能全面测试");
        details.append("JSON配置功能测试报告\n");
        details.append("===================\n\n");

        // 测试1: JSON解析功能
        TestResult parseTest = testJsonParsing();
        details.append("1. JSON解析测试: ").append(parseTest.success ? "通过" : "失败").append("\n");
        details.append("   ").append(parseTest.message).append("\n\n");
        if (!parseTest.success) allTestsPassed = false;

        // 测试2: 配置存储和读取
        TestResult storageTest = testConfigStorage(context);
        details.append("2. 配置存储测试: ").append(storageTest.success ? "通过" : "失败").append("\n");
        details.append("   ").append(storageTest.message).append("\n\n");
        if (!storageTest.success) allTestsPassed = false;

        // 测试3: 双重获取机制
        TestResult fallbackTest = testFallbackMechanism(context);
        details.append("3. 双重获取机制测试: ").append(fallbackTest.success ? "通过" : "失败").append("\n");
        details.append("   ").append(fallbackTest.message).append("\n\n");
        if (!fallbackTest.success) allTestsPassed = false;

        // 测试4: 数据一致性
        TestResult consistencyTest = testDataConsistency(context);
        details.append("4. 数据一致性测试: ").append(consistencyTest.success ? "通过" : "失败").append("\n");
        details.append("   ").append(consistencyTest.message).append("\n\n");
        if (!consistencyTest.success) allTestsPassed = false;

        // 测试5: 向后兼容性
        TestResult compatibilityTest = testBackwardCompatibility(context);
        details.append("5. 向后兼容性测试: ").append(compatibilityTest.success ? "通过" : "失败").append("\n");
        details.append("   ").append(compatibilityTest.message).append("\n\n");
        if (!compatibilityTest.success) allTestsPassed = false;

        String finalMessage = allTestsPassed ? "所有测试通过" : "部分测试失败";
        details.append("测试结果: ").append(finalMessage).append("\n");

        Log.i(TAG, "JSON配置功能测试完成: " + finalMessage);
        return new TestResult(allTestsPassed, finalMessage, details.toString());
    }

    /**
     * 测试JSON解析功能
     */
    private static TestResult testJsonParsing() {
        try {
            String testJson = createTestJsonConfig();
            
            // 测试解析
            JsonConfigParser.ParseResult result = JsonConfigParser.parseJsonConfig(testJson);
            if (!result.success) {
                return new TestResult(false, "JSON解析失败: " + result.errorMessage, "");
            }

            // 验证解析结果
            if (result.apiEndpoints.size() != 2) {
                return new TestResult(false, "API端点数量不正确，期望2个，实际" + result.apiEndpoints.size() + "个", "");
            }

            if (result.batchConfig == null || !result.batchConfig.isEnabled()) {
                return new TestResult(false, "批量配置解析失败或未启用", "");
            }

            // 测试验证功能
            if (!JsonConfigParser.validateJsonConfig(testJson)) {
                return new TestResult(false, "JSON配置验证失败", "");
            }

            return new TestResult(true, "JSON解析和验证功能正常", "");

        } catch (Exception e) {
            Log.e(TAG, "JSON解析测试异常", e);
            return new TestResult(false, "JSON解析测试异常: " + e.getMessage(), "");
        }
    }

    /**
     * 测试配置存储和读取
     */
    private static TestResult testConfigStorage(Context context) {
        try {
            DownloadConfigManager configManager = DownloadConfigManager.getInstance(context);
            String testJson = createTestJsonConfig();

            // 保存JSON配置
            configManager.setJsonConfig(testJson);

            // 读取JSON配置
            String savedJson = configManager.getJsonConfig();
            if (!testJson.equals(savedJson)) {
                return new TestResult(false, "JSON配置存储后读取不一致", "");
            }

            // 测试解析缓存
            JsonConfigParser.ParseResult result1 = configManager.parseJsonConfig();
            JsonConfigParser.ParseResult result2 = configManager.parseJsonConfig();
            
            if (!result1.success || !result2.success) {
                return new TestResult(false, "JSON配置解析失败", "");
            }

            return new TestResult(true, "配置存储和读取功能正常", "");

        } catch (Exception e) {
            Log.e(TAG, "配置存储测试异常", e);
            return new TestResult(false, "配置存储测试异常: " + e.getMessage(), "");
        }
    }

    /**
     * 测试双重获取机制
     */
    private static TestResult testFallbackMechanism(Context context) {
        try {
            DownloadConfigManager configManager = DownloadConfigManager.getInstance(context);
            
            // 清除URL配置，模拟URL获取失败
            configManager.setApiEndpoints(null);
            
            // 设置JSON配置
            String testJson = createTestJsonConfig();
            configManager.setJsonConfig(testJson);

            // 测试API端点获取
            List<ApiEndpoint> endpoints = configManager.getApiEndpointsWithFallback();
            if (endpoints.isEmpty()) {
                return new TestResult(false, "双重获取机制失败，无法获取API端点", "");
            }

            // 测试批量配置获取
            BatchConfig batchConfig = configManager.getBatchConfigWithFallback();
            if (batchConfig == null || !batchConfig.isEnabled()) {
                return new TestResult(false, "双重获取机制失败，无法获取批量配置", "");
            }

            // 验证配置来源
            String configSource = configManager.getConfigSource();
            if (!"JSON配置".equals(configSource)) {
                return new TestResult(false, "配置来源识别错误，期望'JSON配置'，实际'" + configSource + "'", "");
            }

            return new TestResult(true, "双重获取机制工作正常", "");

        } catch (Exception e) {
            Log.e(TAG, "双重获取机制测试异常", e);
            return new TestResult(false, "双重获取机制测试异常: " + e.getMessage(), "");
        }
    }

    /**
     * 测试数据一致性
     */
    private static TestResult testDataConsistency(Context context) {
        try {
            DownloadConfigManager configManager = DownloadConfigManager.getInstance(context);
            String testJson = createTestJsonConfig();
            
            // 解析JSON配置
            JsonConfigParser.ParseResult parseResult = JsonConfigParser.parseJsonConfig(testJson);
            
            // 保存并重新获取
            configManager.setJsonConfig(testJson);
            JsonConfigParser.ParseResult managerResult = configManager.parseJsonConfig();

            // 比较API端点数量
            if (parseResult.apiEndpoints.size() != managerResult.apiEndpoints.size()) {
                return new TestResult(false, "API端点数量不一致", "");
            }

            // 比较批量配置
            if (parseResult.batchConfig.isEnabled() != managerResult.batchConfig.isEnabled()) {
                return new TestResult(false, "批量配置状态不一致", "");
            }

            return new TestResult(true, "数据一致性验证通过", "");

        } catch (Exception e) {
            Log.e(TAG, "数据一致性测试异常", e);
            return new TestResult(false, "数据一致性测试异常: " + e.getMessage(), "");
        }
    }

    /**
     * 测试向后兼容性
     */
    private static TestResult testBackwardCompatibility(Context context) {
        try {
            DownloadConfigManager configManager = DownloadConfigManager.getInstance(context);
            
            // 测试原有配置方式
            JSONObject legacyConfig = new JSONObject();
            legacyConfig.put("server_url", "https://test.example.com");
            legacyConfig.put("auth_token", "test_token");
            legacyConfig.put("max_batch_size", 50);
            legacyConfig.put("name", "test_api");
            
            configManager.setConfig(DownloadStrategyType.PYTHON_LIKE, legacyConfig);
            
            // 验证原有配置仍然可用
            JSONObject retrievedConfig = configManager.getConfig(DownloadStrategyType.PYTHON_LIKE);
            if (!retrievedConfig.optString("server_url").equals("https://test.example.com")) {
                return new TestResult(false, "原有配置方式失效", "");
            }

            return new TestResult(true, "向后兼容性验证通过", "");

        } catch (Exception e) {
            Log.e(TAG, "向后兼容性测试异常", e);
            return new TestResult(false, "向后兼容性测试异常: " + e.getMessage(), "");
        }
    }

    /**
     * 创建测试用的JSON配置
     */
    private static String createTestJsonConfig() {
        return "{\n" +
                "  \"max_workers\": 4,\n" +
                "  \"auth_token\": \"test_token\",\n" +
                "  \"server_url\": \"https://test.example.com\",\n" +
                "  \"api_endpoints\": [\n" +
                "    {\n" +
                "      \"url\": \"https://api1.test.com/chapter?id={chapter_id}\",\n" +
                "      \"name\": \"test_api1\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"url\": \"https://api2.test.com/content\",\n" +
                "      \"name\": \"test_api2\",\n" +
                "      \"data\": {\n" +
                "        \"item_id\": \"{chapter_id}\"\n" +
                "      }\n" +
                "    }\n" +
                "  ],\n" +
                "  \"batch_config\": {\n" +
                "    \"name\": \"test_batch\",\n" +
                "    \"base_url\": \"https://batch.test.com\",\n" +
                "    \"batch_endpoint\": \"/batch\",\n" +
                "    \"max_batch_size\": 100,\n" +
                "    \"enabled\": true\n" +
                "  }\n" +
                "}";
    }
}
