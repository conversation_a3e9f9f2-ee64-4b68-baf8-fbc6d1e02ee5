package com.fanqie.novel.ui.fragment;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.TransitionDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.inputmethod.InputMethodManager;
import android.widget.*;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.alibaba.fastjson.JSON;
import com.fanqie.novel.R;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.domain.Search;
import com.fanqie.novel.ui.activity.BookDetailActivity;
import com.fanqie.novel.ui.adapter.SearchResultAdapter;
import com.fanqie.novel.util.LogUtil;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SearchFragment extends Fragment implements SearchResultAdapter.OnBookClickListener {

    private static final String TAG = "SearchFragment";

    private CardView searchCard;
    private EditText searchEditText;
    private ImageButton clearButton;
    private TextView searchButton;
    private RecyclerView recyclerView;
    private ProgressBar progressBar;
    private FloatingActionButton backToTopButton;
    private TextView searchHintText;
    
    private SearchResultAdapter adapter;
    private final List<BookInfo> searchResults = new ArrayList<>();
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private Search searchProcessor;
    private final long searchDelayMillis = 300;
    private Runnable searchRunnable;
    private boolean hasSearchResults = false;
    
    // 新增核心状态变量
    private String searchQuery = "";
    private boolean isSearching = false;
    
    public SearchFragment() {
        // 必需的空构造函数
    }

    public static SearchFragment newInstance() {
        return new SearchFragment();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.fragment_search, container, false);
        
        // 初始化视图
        searchCard = rootView.findViewById(R.id.search_card);
        searchEditText = rootView.findViewById(R.id.search_edit_text);
        clearButton = rootView.findViewById(R.id.clear_button);
        searchButton = rootView.findViewById(R.id.search_button);
        recyclerView = rootView.findViewById(R.id.search_results_recycler_view);
        progressBar = rootView.findViewById(R.id.search_progress_bar);
        backToTopButton = rootView.findViewById(R.id.back_to_top_button);
        searchHintText = rootView.findViewById(R.id.search_hint_text);
        
        return rootView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 初始化搜索处理器
        searchProcessor = new Search();
        
        // 设置RecyclerView
        setupRecyclerView();
        
        // 设置搜索输入监听
        setupSearchInput();
        
        // 设置按钮点击事件
        setupButtonListeners();
        
        // 初始化UI状态
        updateSearchInputBackground(false);
        
        // 初始化搜索输入框背景为过渡drawable
        TransitionDrawable transitionBackground = (TransitionDrawable) 
                getResources().getDrawable(R.drawable.search_input_background_transition);
        searchEditText.setBackground(transitionBackground);

        // 搜索区聚焦/失焦动画
        searchEditText.setOnFocusChangeListener((v, hasFocus) -> {
            float startElevation = searchCard.getCardElevation();
            float endElevation = hasFocus ? 16f : 4f;
            ValueAnimator elevationAnimator = ValueAnimator.ofFloat(startElevation, endElevation);
            elevationAnimator.setDuration(200);
            elevationAnimator.addUpdateListener(animator ->
                    searchCard.setCardElevation((float) animator.getAnimatedValue()));
            elevationAnimator.start();
            // 可选：调整CardView边距
            ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) searchCard.getLayoutParams();
            int startMargin = params.topMargin;
            int endMargin = hasFocus ? 4 : 12;
            ValueAnimator marginAnimator = ValueAnimator.ofInt(startMargin, endMargin);
            marginAnimator.setDuration(200);
            marginAnimator.addUpdateListener(animator -> {
                params.topMargin = (int) animator.getAnimatedValue();
                searchCard.setLayoutParams(params);
            });
            marginAnimator.start();
        });
    }
    
    private void setupRecyclerView() {
        adapter = new SearchResultAdapter(searchResults, this);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        recyclerView.setAdapter(adapter);
        
        // 监听滚动状态，显示/隐藏回到顶部按钮
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                // 仅在isSearching为true且有结果时才响应
                if (isSearching && !searchResults.isEmpty()) {
                    int offset = recyclerView.computeVerticalScrollOffset();
                    int height = recyclerView.getHeight();
                    if (offset > height) {
                        backToTopButton.show();
                    } else {
                        backToTopButton.hide();
                    }
                } else {
                    backToTopButton.hide();
                }
            }
        });
    }
    
    private void setupSearchInput() {
        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 更新searchQuery变量
                searchQuery = s.toString();
                // 显示或隐藏清除按钮
                clearButton.setVisibility(!searchQuery.isEmpty() ? View.VISIBLE : View.GONE);

                // 延迟搜索，防止频繁请求
                if (searchRunnable != null) {
                    mainHandler.removeCallbacks(searchRunnable);
                }

                searchRunnable = () -> {
                    String query = searchQuery.trim();
                    if (!query.isEmpty()) {
                        isSearching = true;
                        performSearch(query);
                    } else {
                        isSearching = false;
                        clearSearchResults();
                    }
                };

                mainHandler.postDelayed(searchRunnable, searchDelayMillis);
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
    }
    
    private void setupButtonListeners() {
        // 清除按钮
        clearButton.setOnClickListener(v -> {
            searchEditText.setText("");
            searchQuery = "";
            isSearching = false;
            clearSearchResults();
            hideKeyboard();
        });
        
        // 搜索按钮
        searchButton.setOnClickListener(v -> {
            String query = searchEditText.getText().toString().trim();
            if (!query.isEmpty()) {
                searchQuery = query;
                isSearching = true;
                performSearch(query);
                hideKeyboard();
            }
        });
        
        // EditText回车键触发搜索
        searchEditText.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH) {
                String query = searchEditText.getText().toString().trim();
                if (!query.isEmpty()) {
                    searchQuery = query;
                    isSearching = true;
                    performSearch(query);
                    hideKeyboard();
                }
                return true;
            }
            return false;
        });
        
        // 回到顶部按钮
        backToTopButton.setOnClickListener(v -> {
            recyclerView.smoothScrollToPosition(0);
            // 滚动到顶部后隐藏按钮
            backToTopButton.hide();
        });
    }
    
    private void performSearch(String query) {
        showLoading(true);
        
        executorService.execute(() -> {
            try {
                String resultJson = searchProcessor.search(query);
                List<BookInfo> results = JSON.parseArray(resultJson, BookInfo.class);
                
                mainHandler.post(() -> {
                    updateSearchResults(results);
                    showLoading(false);
                });
            } catch (Exception e) {
                LogUtil.e(TAG, "搜索出错", e);
                mainHandler.post(() -> {
                    showLoading(false);
                    updateSearchInputBackground(false);
                    hideBackToTopButton();
                    // 错误反馈
                    Toast.makeText(requireContext(), "搜索出错，请重试", Toast.LENGTH_SHORT).show();
                    searchHintText.setText("搜索出错，请重试");
                    searchHintText.setVisibility(View.VISIBLE);
                    recyclerView.animate().alpha(0f).setDuration(200).withEndAction(() -> recyclerView.setVisibility(View.GONE)).start();
                });
            }
        });
    }
    
    private void updateSearchResults(List<BookInfo> results) {
        searchResults.clear();
        boolean hasResults = results != null && !results.isEmpty();
        if (hasResults) {
            searchResults.addAll(results);
        } else {
            hideBackToTopButton();
        }
        adapter.notifyDataSetChanged();
        updateSearchInputBackground(hasResults);
        // 结果区显隐动画
        if (isSearching) {
            recyclerView.setVisibility(View.VISIBLE);
            recyclerView.animate().alpha(1f).setDuration(200).start();
            searchHintText.setVisibility(View.GONE);
        } else {
            recyclerView.animate().alpha(0f).setDuration(200).withEndAction(() -> recyclerView.setVisibility(View.GONE)).start();
            if (searchQuery.isEmpty()) {
                searchHintText.setText("请输入搜索内容");
            } else {
                searchHintText.setText("未找到相关结果");
            }
            searchHintText.setVisibility(View.VISIBLE);
        }
    }
    
    /**
     * 根据是否有搜索结果更新搜索框背景
     * @param hasResults 是否有搜索结果
     */
    private void updateSearchInputBackground(boolean hasResults) {
        // 如果状态没有变化，不执行动画
        if (hasResults == hasSearchResults) {
            return;
        }
        
        hasSearchResults = hasResults;
        
        // 获取搜索框背景作为过渡Drawable
        TransitionDrawable transitionBackground;
        if (!(searchEditText.getBackground() instanceof TransitionDrawable)) {
            // 如果背景不是TransitionDrawable，初始化为过渡Drawable
            transitionBackground = (TransitionDrawable) 
                    getResources().getDrawable(R.drawable.search_input_background_transition);
            searchEditText.setBackground(transitionBackground);
        } else {
            transitionBackground = (TransitionDrawable) searchEditText.getBackground();
        }
        
        if (hasResults) {
            // 添加搜索卡片的动画效果
            animateSearchCard(true);
            
            // 使用ValueAnimator实现背景色过渡
            final int startColor = getResources().getColor(android.R.color.white);
            final int endColor = getResources().getColor(android.R.color.white);
            
            ValueAnimator colorAnimator = ValueAnimator.ofArgb(startColor, endColor);
            colorAnimator.setDuration(300); // 300ms动画时间，与WebView一致
            colorAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
            colorAnimator.addUpdateListener(animator -> {
                int animatedColor = (int) animator.getAnimatedValue();
                searchCard.setCardBackgroundColor(animatedColor);
            });
            colorAnimator.start();
            
            // 有搜索结果时，将过渡动画切换到第二个状态（白色背景）
            transitionBackground.startTransition(300);
        } else {
            // 添加搜索卡片的动画效果
            animateSearchCard(false);
            
            // 无搜索结果时，将过渡动画切换回第一个状态（浅灰色背景）
            transitionBackground.reverseTransition(300);
            
            // 如果没有搜索结果，确保隐藏回到顶部按钮
            hideBackToTopButton();
        }
    }
    
    /**
     * 动画效果实现搜索卡片的视觉变化
     * @param isSearching 是否处于搜索状态
     */
    private void animateSearchCard(boolean isSearching) {
        // 初始化卡片的当前参数
        float currentElevation = searchCard.getCardElevation();
        int currentPaddingStart = searchCard.getPaddingStart();
        int currentPaddingTop = searchCard.getPaddingTop();
        int currentPaddingEnd = searchCard.getPaddingEnd();
        int currentPaddingBottom = searchCard.getPaddingBottom();
        
        // 定义动画目标值
        float targetElevation = isSearching ? 16f : 4f; // 搜索时增加阴影
        int targetPaddingHorizontal = isSearching ? 8 : 12;
        int targetPaddingVertical = isSearching ? 12 : 8;
        
        // 创建卡片阴影动画
        ValueAnimator elevationAnimator = ValueAnimator.ofFloat(currentElevation, targetElevation);
        elevationAnimator.setDuration(300);
        elevationAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        elevationAnimator.addUpdateListener(animator -> 
            searchCard.setCardElevation((float) animator.getAnimatedValue()));
        
        // 创建卡片内边距动画
        ValueAnimator paddingAnimator = ValueAnimator.ofFloat(0f, 1f);
        paddingAnimator.setDuration(300);
        paddingAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        paddingAnimator.addUpdateListener(animator -> {
            float fraction = (float) animator.getAnimatedValue();
            int paddingStart = (int) (currentPaddingStart + fraction * (targetPaddingHorizontal - currentPaddingStart));
            int paddingTop = (int) (currentPaddingTop + fraction * (targetPaddingVertical - currentPaddingTop));
            int paddingEnd = (int) (currentPaddingEnd + fraction * (targetPaddingHorizontal - currentPaddingEnd));
            int paddingBottom = (int) (currentPaddingBottom + fraction * (targetPaddingVertical - currentPaddingBottom));
            
            searchCard.setPadding(paddingStart, paddingTop, paddingEnd, paddingBottom);
        });
        
        // 启动动画
        elevationAnimator.start();
        paddingAnimator.start();
    }
    
    /**
     * 隐藏回到顶部按钮
     */
    private void hideBackToTopButton() {
        if (backToTopButton != null && backToTopButton.isShown()) {
            backToTopButton.hide();
        }
    }
    
    private void clearSearchResults() {
        searchResults.clear();
        adapter.notifyDataSetChanged();
        updateSearchInputBackground(false);
        hideBackToTopButton();
        // 结果区显隐动画
        recyclerView.animate().alpha(0f).setDuration(200).withEndAction(() -> recyclerView.setVisibility(View.GONE)).start();
        searchHintText.setText("请输入搜索内容");
        searchHintText.setVisibility(View.VISIBLE);
    }
    
    private void showLoading(boolean isLoading) {
        progressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
    }
    
    private void hideKeyboard() {
        InputMethodManager imm = (InputMethodManager) requireContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(searchEditText.getWindowToken(), 0);
    }

    @Override
    public void onBookClick(BookInfo book) {
        // 跳转到BookDetailActivity，携带完整BookInfo数据
        Intent intent = new Intent(requireActivity(), BookDetailActivity.class);
        intent.putExtra("bookId", book.getBookId());
        intent.putExtra("imgUrl", book.getImageUrl());
        intent.putExtra("bookName", book.getBookName());
        intent.putExtra("bookInfoJson", JSON.toJSONString(book));
        startActivity(intent);
    }
    
    /**
     * 为兼容MainActivity中的调用，添加canGoBack()方法
     * 由于现在是原生实现，不再有WebView的goBack功能，所以永远返回false
     */
    public boolean canGoBack() {
        // 原生实现不需要后退功能，始终返回false
        return false;
    }
    
    /**
     * 为兼容MainActivity中的调用，添加goBack()方法
     * 由于现在是原生实现，这个方法不做任何操作
     */
    public void goBack() {
        // 原生实现不需要后退操作，此方法为空实现
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        executorService.shutdown();
    }

    /**
     * 实现搜索卡片的"sticky"效果，模拟WebView的position:sticky
     * @param recyclerView 列表视图
     */
    private void implementStickyEffect(RecyclerView recyclerView) {
        int scrollOffset = recyclerView.computeVerticalScrollOffset();
        
        // 当滚动超过一定阈值时，修改卡片外观，实现"sticky"效果
        if (scrollOffset > 50) {
            // 转变为固定在顶部的效果
            if (searchCard.getTranslationZ() != 8f) {
                // 使用动画提升卡片的Z轴高度，增强视觉效果
                ValueAnimator animator = ValueAnimator.ofFloat(searchCard.getTranslationZ(), 8f);
                animator.setDuration(200);
                animator.setInterpolator(new AccelerateDecelerateInterpolator());
                animator.addUpdateListener(valueAnimator -> 
                    searchCard.setTranslationZ((float) valueAnimator.getAnimatedValue()));
                animator.start();
                
                // 增加阴影效果
                searchCard.setCardElevation(16f);
                
                // 调整卡片边距，使其看起来像"粘"在顶部
                int originalMargin = ((ViewGroup.MarginLayoutParams) searchCard.getLayoutParams()).topMargin;
                ValueAnimator marginAnimator = ValueAnimator.ofInt(originalMargin, 0);
                marginAnimator.setDuration(200);
                marginAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
                marginAnimator.addUpdateListener(valueAnimator -> {
                    ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) searchCard.getLayoutParams();
                    params.topMargin = (int) valueAnimator.getAnimatedValue();
                    searchCard.setLayoutParams(params);
                });
                marginAnimator.start();
            }
        } else {
            // 恢复初始状态
            if (searchCard.getTranslationZ() != 0f) {
                // 使用动画恢复卡片的Z轴高度
                ValueAnimator animator = ValueAnimator.ofFloat(searchCard.getTranslationZ(), 0f);
                animator.setDuration(200);
                animator.setInterpolator(new AccelerateDecelerateInterpolator());
                animator.addUpdateListener(valueAnimator -> 
                    searchCard.setTranslationZ((float) valueAnimator.getAnimatedValue()));
                animator.start();
                
                // 恢复原始阴影
                searchCard.setCardElevation(hasSearchResults ? 16f : 4f);
                
                // 恢复原始边距
                int targetMargin = 12; // 原始的顶部边距
                ValueAnimator marginAnimator = ValueAnimator.ofInt(
                        ((ViewGroup.MarginLayoutParams) searchCard.getLayoutParams()).topMargin, 
                        targetMargin);
                marginAnimator.setDuration(200);
                marginAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
                marginAnimator.addUpdateListener(valueAnimator -> {
                    ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) searchCard.getLayoutParams();
                    params.topMargin = (int) valueAnimator.getAnimatedValue();
                    searchCard.setLayoutParams(params);
                });
                marginAnimator.start();
            }
        }
    }
} 