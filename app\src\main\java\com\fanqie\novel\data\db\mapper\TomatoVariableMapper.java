package com.fanqie.novel.data.db.mapper;

import android.content.Context;
import android.database.Cursor;
import com.fanqie.novel.data.model.TomatoVariable;


public class TomatoVariableMapper extends BaseMapper {
    private static volatile TomatoVariableMapper instance;

    protected TomatoVariableMapper(Context context) {
        super(context);
    }

    public static TomatoVariableMapper getInstance(Context context) {
        if (instance == null) {
            synchronized (BookMapper.class) {
                if (instance == null) {
                    instance = new TomatoVariableMapper(context.getApplicationContext());
                }
            }
        }
        return instance;
    }

    /**
     * 向tomato_variable表中插入变量信息
     *
     * @param installId 安装ID，用于标识安装实例
     * @param serverDeviceId 服务器设备ID，与设备相关联
     * @param aid 辅助ID，额外的标识信息
     * @param updateVersionCode 更新版本代码，表示当前安装的版本
     */
    public void insertTomatoVariable(String installId, String serverDeviceId, String aid, String updateVersionCode) {
        // 准备SQL语句模板，用于插入数据到tomato_variable表中
        String sql = "insert into tomato_variable(install_id, server_device_id, aid, update_version_code) values(?, ?, ?, ?)";
        // 执行SQL语句，插入数据
        db.execSQL(sql, new Object[]{installId, serverDeviceId, aid, updateVersionCode});
    }

    /**
     * 更新tomato_variable表中的特定记录
     *
     * 此方法用于更新数据库中tomato_variable表的特定记录，主要更新install_id（安装ID）、server_device_id（服务器设备ID）、
     * aid（辅助ID）和update_version_code（更新版本代码）这四个字段的值它通过匹配提供的install_id来定位需要更新的记录
     *
     * @param installId 安装ID，用于定位需要更新的记录
     * @param serverDeviceId 服务器设备ID，需要更新的字段之一
     * @param aid 辅助ID，需要更新的字段之一
     * @param updateVersionCode 更新版本代码，需要更新的字段之一
     */
    public void updateTomatoVariable(String installId, String serverDeviceId, String aid, String updateVersionCode) {
        // 定义SQL更新语句，使用占位符来防止SQL注入攻击，并准备更新所需的参数
        String sql = "update tomato_variable set install_id = ?, server_device_id = ?, aid = ?, update_version_code = ? where install_id = ?";
        // 执行更新操作，传递参数数组和匹配条件
        db.execSQL(sql, new Object[]{installId, serverDeviceId, aid, updateVersionCode, installId});
    }

    /**
     * 查询数据库中最新的TomatoVariable变量信息
     *
     * 该方法通过查询数据库tomato_variable表中的最新记录来获取TomatoVariable对象
     * 使用SQL查询语句获取最新的一条记录，并将其封装成TomatoVariable对象返回
     * 如果数据库中没有记录，则返回null
     *
     * @return 最新的TomatoVariable对象，如果没有数据则返回null
     */
    public TomatoVariable queryTomatoVariable() {
        TomatoVariable tomatoVariable = null;
        // SQL查询语句，用于获取tomato_variable表中最新的记录
        String sql = "select * from tomato_variable order by id desc limit 1";
        // 执行SQL查询，并自动管理数据库连接的关闭
        try (Cursor cursor = db.rawQuery(sql, null)) {
            // 遍历查询结果，这里只会有最多一条记录，所以循环一次后立即结束
            while (cursor.moveToNext()) {
                // 将查询到的记录封装成TomatoVariable对象
                tomatoVariable = new TomatoVariable(cursor.getString(cursor.getColumnIndexOrThrow("install_id")),
                        cursor.getString(cursor.getColumnIndexOrThrow("server_device_id")),
                        cursor.getString(cursor.getColumnIndexOrThrow("aid")),
                        cursor.getString(cursor.getColumnIndexOrThrow("update_version_code"))
                );
            }
        }
        // 返回封装好的TomatoVariable对象，或者null（如果没有查询到记录）
        return tomatoVariable;
    }

}
