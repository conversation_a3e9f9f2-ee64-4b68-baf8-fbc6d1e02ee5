package com.fanqie.novel.service;

import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import com.fanqie.novel.constants.CommonCache;
import com.fanqie.novel.domain.NovelDownloadTask;
import com.fanqie.novel.util.LogUtil;

import java.util.Locale;

import static com.fanqie.novel.constants.BookConstants.CHANNEL_ID;

/**
 * 下载服务类
 * 负责在后台执行书籍下载任务，并显示通知栏进度
 *
 * 该服务会在前台运行，显示一个持续的通知来指示下载进度
 * 通过 DownloadTask 执行实际的下载操作
 * 
 * <AUTHOR>
 * @see NotificationCompat.Builder
 */
public class NovelDownloadService extends Service {
    private static final String TAG = "DownloadService";

    /**
     * 通知构建器
     * 用于创建和更新下载进度通知
     */
    private NotificationCompat.Builder builder;
    
    /**
     * 通知管理器
     * 用于管理通知的显示和更新
     */
    private NotificationManager notificationManager;

    /**
     * 服务创建时的回调方法
     * 初始化服务所需的资源
     */
    @Override
    public void onCreate() {
        super.onCreate();
        notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
    }

    /**
     * 处理下载服务的启动请求
     * 接收书籍ID并创建下载任务
     * <p>
     * 该方法会创建一个前台通知，并启动下载任务
     * 如果没有收到有效的bookId，将记录警告日志
     *
     * @param intent  包含书籍ID的Intent，通过"bookId"键传递
     * @param flags   服务启动标志
     * @param startId 启动ID
     * @return START_STICKY 表示服务被杀死后应该重新创建
     */
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String bookId = intent.getStringExtra("bookId");
            LogUtil.setMdcContext(bookId);
            if (bookId != null) {
                LogUtil.i(TAG, String.format(Locale.getDefault(), "开始下载服务: bookId=%s", bookId));
                initNotification(bookId);

                NovelDownloadTask downloadTask = new NovelDownloadTask(bookId, this);
                downloadTask.execute();
            } else {
                LogUtil.w(TAG, "未收到有效的bookId");
            }
        } else {
            LogUtil.w(TAG, "收到空的Intent");
        }
        return START_STICKY;
    }

    /**
     * 初始化通知栏
     * 创建并显示下载进度通知
     *
     * 该方法会创建一个通知构建器，供下载进度更新使用
     *
     * @param bookId 书籍ID，用于获取书籍名称和通知ID
     */
    private void initNotification(String bookId) {
        LogUtil.d(TAG, "初始化通知构建器: bookId=" + bookId);
        builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.stat_sys_download)
                .setContentTitle(CommonCache.getBookName(bookId))
                .setContentText("正在下载书籍，请稍候...")
                .setProgress(0, 0, false);
        
        // 注意：不发送通知，让BookshelfFragment负责通知显示
    }

    /**
     * 绑定服务时的回调方法
     * 本服务不支持绑定，返回null
     *
     * @param intent 绑定服务的Intent
     * @return null 表示不支持绑定
     */
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    /**
     * 获取通知构建器
     * 用于更新下载进度通知
     *
     * 该方法供DownloadTask使用，用于更新下载进度
     *
     * @return 通知构建器实例
     */
    public NotificationCompat.Builder getBuilder() {
        return builder;
    }
    
    /**
     * 获取通知管理器
     * 用于更新和取消通知
     *
     * @return 通知管理器实例
     */
    public NotificationManager getNotificationManager() {
        return notificationManager;
    }

    /**
     * 服务销毁时的回调方法
     * 清理服务使用的资源
     */
    @Override
    public void onDestroy() {
        LogUtil.clearMdcContext();
        super.onDestroy();
    }
}