package com.fanqie.novel.data.db.mapper;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import com.fanqie.novel.data.db.DatabaseHelper;

public abstract class BaseMapper {
    protected final SQLiteDatabase db;
    protected final DatabaseHelper dbHelper;
    protected final Context context;

    protected BaseMapper(Context context) {
        this.context = context;
        this.dbHelper = DatabaseHelper.getInstance(context);
        this.db = dbHelper.getWritableDatabase();
    }

    public Context getContext() {
        return context;
    }
} 