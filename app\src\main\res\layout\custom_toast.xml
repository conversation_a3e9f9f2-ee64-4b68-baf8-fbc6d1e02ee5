<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:background="@drawable/toast_background"
              android:gravity="center"
              android:orientation="horizontal"
              android:padding="16dp">

    <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="8dp"
            android:scaleType="centerInside"
            android:adjustViewBounds="true"
            android:src="@mipmap/ic_launcher_round"
            android:contentDescription="@null"/>

    <TextView
            android:id="@+id/toast_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFF"
            android:textSize="14sp"/>

</LinearLayout> 