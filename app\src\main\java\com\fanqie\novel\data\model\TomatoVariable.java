package com.fanqie.novel.data.model;

import cn.hutool.core.convert.Convert;

public class TomatoVariable {
    private final Long installId;
    private final Long serverDeviceId;
    private final String aid;
    private final String updateVersionCode;

    public TomatoVariable(String installId, String serverDeviceId, String aid, String updateVersionCode) {
        // 使用Hutool的安全类型转换（失败返回null）
        this.installId = Convert.toLong(installId);
        this.serverDeviceId = Convert.toLong(serverDeviceId);
        this.aid = aid;
        this.updateVersionCode = updateVersionCode;
    }

    public Long getInstallId() {
        return installId;
    }

    public Long getServerDeviceId() {
        return serverDeviceId;
    }

    public String getAid() {
        return aid;
    }

    public String getUpdateVersionCode() {
        return updateVersionCode;
    }
}