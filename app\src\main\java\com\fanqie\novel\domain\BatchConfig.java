package com.fanqie.novel.domain;

public class BatchConfig {
    private String baseUrl;
    private String batchEndpoint;
    private String token;
    private int maxBatchSize;
    private int timeout;
    private boolean enabled;

    public BatchConfig() {}

    public BatchConfig(String baseUrl, String batchEndpoint, String token, int maxBatchSize, int timeout, boolean enabled) {
        this.baseUrl = baseUrl;
        this.batchEndpoint = batchEndpoint;
        this.token = token;
        this.maxBatchSize = maxBatchSize;
        this.timeout = timeout;
        this.enabled = enabled;
    }

    public String getBaseUrl() { return baseUrl; }
    public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }
    public String getBatchEndpoint() { return batchEndpoint; }
    public void setBatchEndpoint(String batchEndpoint) { this.batchEndpoint = batchEndpoint; }
    public String getToken() { return token; }
    public void setToken(String token) { this.token = token; }
    public int getMaxBatchSize() { return maxBatchSize; }
    public void setMaxBatchSize(int maxBatchSize) { this.maxBatchSize = maxBatchSize; }
    public int getTimeout() { return timeout; }
    public void setTimeout(int timeout) { this.timeout = timeout; }
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
} 