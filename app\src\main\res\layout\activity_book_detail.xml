<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    tools:context=".BookDetailActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/purple_66b"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Dark">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/white"
                android:textSize="20sp"
                tools:text="书名"/>
                
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nested_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- 顶部信息区域 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="15dp">

                        <ImageView
                            android:id="@+id/iv_book_cover"
                            android:layout_width="100dp"
                            android:layout_height="148dp"
                            android:layout_marginRight="15dp"
                            android:scaleType="centerCrop"
                            android:background="#EEEEEE"
                            tools:src="@drawable/default_cover" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_book_title"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:layout_marginBottom="6dp"
                                tools:text="书名" />

                            <TextView
                                android:id="@+id/tv_book_author"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#666666"
                                android:textSize="14sp"
                                tools:text="作者：某某" />

                            <TextView
                                android:id="@+id/tv_word_count"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#666666"
                                android:textSize="14sp"
                                tools:text="字数：100万" />

                            <TextView
                                android:id="@+id/tv_last_chapter"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#666666"
                                android:textSize="14sp"
                                tools:text="最新章节：第100章" />

                            <TextView
                                android:id="@+id/tv_update_time"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#666666"
                                android:textSize="14sp"
                                tools:text="更新时间：2023-05-01 12:30" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- 下载进度布局 -->
                    <LinearLayout
                        android:id="@+id/download_progress_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone"
                        android:layout_marginBottom="10dp">

                        <TextView
                            android:id="@+id/download_progress_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="#666666"
                            android:textSize="14sp"
                            android:text="下载进度: 0% (0/0)" />

                        <ProgressBar
                            android:id="@+id/download_progress_bar"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btn_download"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="6dp"
                            android:backgroundTint="#1890FF"
                            android:text="下载所有章节"
                            android:textColor="@android:color/white" />

                        <Button
                            android:id="@+id/btn_export"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="6dp"
                            android:backgroundTint="#1890FF"
                            android:text="导出所有章节"
                            android:textColor="@android:color/white" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 标签区域 -->
            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none"
                android:layout_marginBottom="15dp">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_tags"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:chipSpacing="8dp" />
            </HorizontalScrollView>

            <!-- 简介区域 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="15dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="简介"
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="10dp" />

                    <TextView
                        android:id="@+id/tv_book_intro"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#666666"
                        android:textSize="14sp"
                        android:lineSpacingExtra="4dp"
                        android:maxLines="3"
                        android:ellipsize="end"
                        tools:text="这是书籍简介内容..." />

                    <TextView
                        android:id="@+id/tv_expand_intro"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="展开"
                        android:textColor="#1890FF"
                        android:textSize="14sp"
                        android:layout_marginTop="8dp"
                        android:padding="4dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 目录区域 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="15dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="15dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="目录"
                                android:textColor="#333333"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_chapter_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:textColor="#666666"
                                android:textSize="13sp"
                                tools:text="共100章" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_sort_order"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="正序"
                            android:textColor="#1890FF"
                            android:textSize="14sp"
                            android:padding="4dp" />
                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_chapters"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                    <TextView
                        android:id="@+id/tv_load_more"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:padding="15dp"
                        android:text="点击加载更多"
                        android:textColor="#666666"
                        android:background="#F5F5F5"
                        android:layout_marginTop="10dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_back_to_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_arrow_up"
        android:visibility="gone"
        app:backgroundTint="@color/purple_66b"
        app:fabSize="mini"
        app:tint="@android:color/white" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 