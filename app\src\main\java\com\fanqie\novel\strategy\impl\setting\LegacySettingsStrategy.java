package com.fanqie.novel.strategy.impl.setting;

import android.app.Activity;
import com.fanqie.novel.constants.DownloadStrategyType;
import com.fanqie.novel.strategy.ISettingsStrategy;
import com.fanqie.novel.util.DownloadConfigManager;
import com.fanqie.novel.util.ToastUtil;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class LegacySettingsStrategy implements ISettingsStrategy {
    @Override
    public void getConfig(Activity activity, Callback callback) {
        try {
            DownloadConfigManager configManager = DownloadConfigManager.getInstance(activity);
            JSONObject legacy = configManager.getConfig(DownloadStrategyType.LEGACY);
            Map<String, Object> config = new HashMap<>();
            config.put("installId", legacy.optString("installId", ""));
            config.put("serverDeviceId", legacy.optString("serverDeviceId", ""));
            config.put("aid", legacy.optString("aid", ""));
            config.put("updateVersionCode", legacy.optString("updateVersionCode", ""));
            callback.onSuccess(config);
        } catch (Exception e) {
            callback.onFailure("加载默认模式配置失败: " + e.getMessage());
        }
    }

    @Override
    public void updateConfig(Activity activity, Map<String, Object> values, Callback callback) {
        try {
            DownloadConfigManager configManager = DownloadConfigManager.getInstance(activity);
            JSONObject legacy = new JSONObject()
                .put("installId", values.get("installId"))
                .put("serverDeviceId", values.get("serverDeviceId"))
                .put("aid", values.get("aid"))
                .put("updateVersionCode", values.get("updateVersionCode"));
            configManager.setConfig(DownloadStrategyType.LEGACY, legacy);
            callback.onSuccess(values);
        } catch (Exception e) {
            callback.onFailure("保存默认模式配置失败: " + e.getMessage());
        }
    }

    @Override
    public void onSuccess(Activity activity) {
        // 弹Toast并关闭设置页
        ToastUtil.showCustomToast(activity, "设置已保存");
        activity.finish();
    }

    @Override
    public void onFailure(Activity activity, String errorMsg) {
        ToastUtil.showCustomToast(activity, errorMsg);
    }
} 