package com.fanqie.novel.util;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import androidx.activity.result.ActivityResult;
import cn.hutool.core.util.StrUtil;
import com.fanqie.novel.data.db.mapper.BookMapper;
import com.fanqie.novel.data.db.mapper.ChapterMapper;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.data.model.ChapterInfo;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class BookDataExportUtil {
    private static final String TAG = "BookDataExportUtil";
    private static final int BUFFER_SIZE = 8192;
    private static final ExecutorService executorService = Executors.newSingleThreadExecutor();

    // 存储当前正在处理的临时文件路径
    private static String currentTempFilePath;

    /**
     * 异步从数据库获取书籍和章节信息，并创建临时文件
     *
     * @param context       上下文对象，用于获取数据库访问对象和缓存目录
     * @param bookId        书籍ID
     * @param filename      导出的文件名
     * @return 包含临时文件的CompletableFuture对象
     */
    public static CompletableFuture<File> getBookDataAndCreateTempFile(Context context, String bookId, String filename) {
        LogUtil.setMdcContext(bookId); // 设置MDC上下文
        LogUtil.i(TAG, String.format("开始获取书籍数据: bookId=%s, filename=%s", bookId, filename));

        return CompletableFuture.supplyAsync(() -> {
            try {
                LogUtil.setMdcContext(bookId);
                
                // 1. 获取书籍和章节数据
                BookMapper bookMapper = BookMapper.getInstance(context);
                ChapterMapper chapterMapper = ChapterMapper.getInstance(context);
                
                // 获取书籍信息
                List<BookInfo> bookInfoList = bookMapper.getBookList(bookId);
                if (bookInfoList.isEmpty()) {
                    LogUtil.e(TAG, "未找到书籍信息: bookId=" + bookId);
                    return null;
                }
                
                BookInfo bookInfo = bookInfoList.get(0);
                LogUtil.i(TAG, String.format("获取到书籍信息: 书名=%s, 作者=%s", 
                        bookInfo.getBookName(), bookInfo.getAuthor()));
                
                // 获取章节列表
                List<ChapterInfo> chapters = chapterMapper.getChaptersByBookId(bookId);
                LogUtil.i(TAG, String.format(Locale.getDefault(), "获取到 %d 个章节", chapters.size()));
                
                if (chapters.isEmpty()) {
                    LogUtil.w(TAG, "未找到章节信息: bookId=" + bookId);
                }
                
                // 2. 准备文件内容
                LogUtil.d(TAG, "开始准备书籍内容...");
                String fileContent = prepareBookContent(bookInfo, chapters);
                if (StrUtil.isEmpty(fileContent)) {
                    LogUtil.e(TAG, "生成的内容为空: bookId=" + bookId);
                    return null;
                }
                LogUtil.i(TAG, String.format(Locale.getDefault(), "内容准备完成，长度: %d 字符", fileContent.length()));

                // 3. 创建临时文件
                LogUtil.d(TAG, "开始创建临时文件...");
                File tempFile = createTempFile(context, filename);
                LogUtil.i(TAG, "临时文件创建成功: " + tempFile.getAbsolutePath());

                // 4. 写入内容到临时文件
                LogUtil.d(TAG, "开始写入文件内容...");
                boolean writeSuccess = writeContentToFile(tempFile, fileContent);
                if (!writeSuccess) {
                    LogUtil.e(TAG, "写入文件失败");
                    return null;
                }
                LogUtil.i(TAG, String.format(Locale.getDefault(), "文件写入成功，大小: %d 字节", tempFile.length()));

                return tempFile;
            } catch (Exception e) {
                LogUtil.e(TAG, "处理书籍数据过程中发生错误", e);
                return null;
            } finally {
                LogUtil.clearMdcContext(); // 清除MDC上下文
            }
        }, executorService);
    }

    /**
     * 处理文件保存结果
     * 将临时文件复制到用户选择的目标位置
     *
     * @param result          Activity返回的结果，包含目标URI
     * @param contentResolver 内容解析器，用于访问目标URI
     * @param context         上下文对象，用于显示提示信息
     */
    public static void handleSaveResult(ActivityResult result, ContentResolver contentResolver, Context context) {
        LogUtil.i(TAG, "开始处理文件保存结果");

        if (result.getResultCode() != Activity.RESULT_OK || result.getData() == null) {
            LogUtil.w(TAG, "用户取消保存或未收到数据");
            return;
        }

        Uri uri = result.getData().getData();
        if (uri == null) {
            LogUtil.e(TAG, "未收到目标URI");
            if (context != null) {
                ToastUtil.showCustomToast(context, "导出失败：未收到目标位置");
            }
            return;
        }
        LogUtil.d(TAG, "目标保存URI: " + uri);

        CompletableFuture.runAsync(() -> {
            try {
                File tempFile = new File(currentTempFilePath);
                if (!tempFile.exists()) {
                    LogUtil.e(TAG, "临时文件不存在: " + currentTempFilePath);
                    if (context != null) {
                        android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
                        mainHandler.post(() -> ToastUtil.showCustomToast(context, "导出失败：临时文件不存在"));
                    }
                    return;
                }
                copyFileToUri(tempFile, uri, contentResolver);
                
                if (context != null) {
                    android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
                    // 获取文件名作为书籍名（去掉.txt后缀）
                    String bookName = tempFile.getName();
                    if (bookName.endsWith(".txt")) {
                        bookName = bookName.substring(0, bookName.length() - 4);
                    }
                    String finalBookName = bookName;
                    mainHandler.post(() -> ToastUtil.showSuccessToast(context, "《" + finalBookName + "》导出成功"));
                }
            } finally {
                LogUtil.d(TAG, "开始清理临时文件");
                cleanupTempFile(new File(currentTempFilePath));
            }
        }, executorService);
    }

    /**
     * 创建文件选择器Intent
     * 用于让用户选择文件保存位置
     *
     * @param filename 建议的文件名
     * @param tempFile 临时文件，用于记录路径
     * @return 配置好的文件选择器Intent
     */
    public static Intent createSaveIntent(String filename, File tempFile) {
        currentTempFilePath = tempFile.getAbsolutePath();
        LogUtil.d(TAG, "创建保存文件Intent: " + filename);

        Intent intent = new Intent(Intent.ACTION_CREATE_DOCUMENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("text/plain");
        intent.putExtra(Intent.EXTRA_TITLE, filename);

        return intent;
    }

    /**
     * 准备书籍内容
     * 将书籍信息和章节内容组装成完整的文本
     *
     * @param bookInfo 书籍信息
     * @param chapters 章节列表
     * @return 组装好的文本内容
     */
    private static String prepareBookContent(BookInfo bookInfo, List<ChapterInfo> chapters) {
        StringBuilder contentBuilder = new StringBuilder();

        // 添加书籍信息
        contentBuilder.append(bookInfo.getBookName()).append("\n");
        contentBuilder.append("作者：").append(bookInfo.getAuthor()).append("\n");
        contentBuilder.append("标签：").append(bookInfo.getTags()).append("\n");
        contentBuilder.append("字数：").append(bookInfo.getCount()).append("\n");
        contentBuilder.append("\n简介：\n").append(StrUtil.replace(bookInfo.getAbstractContent(), " ", "\n")).append("\n\n");

        // 处理章节内容
        LogUtil.d(TAG, "开始处理章节内容...");
        
        // 按章节排序号排序
        chapters.sort(Comparator.comparingInt(ChapterInfo::getSort));
        
        // 组装章节内容
        for (ChapterInfo chapter : chapters) {
            contentBuilder.append("\n\n")
                     .append(chapter.getTitle())
                     .append("\n\n");
            
            if (chapter.getContent() != null) {
                contentBuilder.append(chapter.getContent());
            }
        }

        return contentBuilder.toString();
    }

    /**
     * 创建临时文件
     * 在应用缓存目录下创建临时文件
     *
     * @param context  上下文对象
     * @param filename 文件名
     * @return 创建的临时文件
     */
    private static File createTempFile(Context context, String filename) throws IOException {
        File cacheDir = context.getCacheDir();
        File tempDir = new File(cacheDir, "book_export");
        if (!tempDir.exists() && !tempDir.mkdirs()) {
            LogUtil.w(TAG, "创建临时目录失败");
        }

        // 清理旧的临时文件
        File[] oldFiles = tempDir.listFiles();
        if (oldFiles != null) {
            for (File oldFile : oldFiles) {
                if (!oldFile.delete()) {
                    LogUtil.w(TAG, "删除旧临时文件失败: " + oldFile.getAbsolutePath());
                }
            }
        }

        // 创建新的临时文件
        File tempFile = new File(tempDir, filename);
        if (!tempFile.createNewFile()) {
            LogUtil.w(TAG, "临时文件已存在，将被覆盖: " + tempFile.getAbsolutePath());
        }

        return tempFile;
    }

    /**
     * 将内容写入文件
     * 使用UTF-8编码写入文本内容
     *
     * @param file    目标文件
     * @param content 要写入的内容
     * @return 是否写入成功
     */
    private static boolean writeContentToFile(File file, String content) {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8))) {
            writer.write(content);
            writer.flush();
            return true;
        } catch (IOException e) {
            LogUtil.e(TAG, "写入文件失败", e);
            return false;
        }
    }

    /**
     * 将源文件复制到目标URI
     * 使用缓冲流提高复制效率
     *
     * @param sourceFile      源文件
     * @param destUri         目标URI
     * @param contentResolver 内容解析器
     */
    private static void copyFileToUri(File sourceFile, Uri destUri, ContentResolver contentResolver) {
        LogUtil.i(TAG, "开始复制文件到目标URI");
        long startTime = System.currentTimeMillis();
        long totalBytes = 0;

        try (InputStream in = new BufferedInputStream(new FileInputStream(sourceFile));
             OutputStream out = new BufferedOutputStream(
                     contentResolver.openOutputStream(destUri))) {

            byte[] buffer = new byte[BUFFER_SIZE];
            int length;
            while ((length = in.read(buffer)) > 0) {
                out.write(buffer, 0, length);
                totalBytes += length;
            }
            out.flush();

            long endTime = System.currentTimeMillis();
            LogUtil.i(TAG, String.format(Locale.getDefault(), "文件复制完成: 总大小=%d bytes, 耗时=%d ms",
                    totalBytes, (endTime - startTime)));
        } catch (IOException e) {
            LogUtil.e(TAG, "复制文件时发生错误", e);
        }
    }

    /**
     * 清理临时文件
     * 在文件处理完成后删除临时文件
     *
     * @param tempFile 要删除的临时文件
     */
    private static void cleanupTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            if (!tempFile.delete()) {
                LogUtil.w(TAG, "删除临时文件失败: " + tempFile.getAbsolutePath());
            } else {
                LogUtil.d(TAG, "临时文件删除成功");
            }
        }
    }
} 