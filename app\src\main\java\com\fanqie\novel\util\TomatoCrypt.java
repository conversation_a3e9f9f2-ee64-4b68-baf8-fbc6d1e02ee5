package com.fanqie.novel.util;

import cn.hutool.core.codec.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.security.SecureRandom;

public class TomatoCrypt {
    private final SecretKeySpec key;
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";

    public TomatoCrypt(String keyHex) {
        if (keyHex.length() != 32) {
            throw new IllegalArgumentException("Key length mismatch! key: " + keyHex);
        }
        this.key = new SecretKeySpec(hexStringToByteArray(keyHex), "AES");
    }

    /**
     * 使用指定算法和初始化向量加密数据
     *
     * @param data 待加密的数据
     * @param iv 初始化向量，用于增加加密的安全性
     * @return 加密后的数据
     *
     * 此方法负责使用指定的算法（ALGORITHM）和给定的初始化向量（iv）来加密输入的数据（data）
     * 它首先获取Cipher对象的实例，该对象支持指定的加密算法然后，它使用提供的初始化向量创建一个
     * IvParameterSpec对象，这有助于提高加密过程的安全性接下来，Cipher对象被初始化为加密模式，
     * 并使用前面准备的密钥和初始化向量最后，数据被加密并返回
     *
     * 注意：此方法假定已经存在一个名为ALGORITHM的算法常量和一个名为key的密钥对象
     */
    public byte[] encrypt(byte[] data, byte[] iv) {
        try {
            // 获取Cipher对象的实例，该对象支持指定的加密算法
            Cipher cipher = Cipher.getInstance(ALGORITHM);

            // 使用提供的初始化向量创建一个IvParameterSpec对象
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            // 初始化Cipher对象为加密模式，并使用前面准备的密钥和初始化向量
            cipher.init(Cipher.ENCRYPT_MODE, key, ivSpec);

            // 执行加密操作，并返回加密后的数据
            return cipher.doFinal(data);
        } catch (Exception e) {
            // 如果出现异常，将其包装为RuntimeException并抛出
            throw new RuntimeException(e);
        }
    }

    /**
     * 使用指定算法和密钥解密数据
     *
     * 此方法首先从输入的字节数组中提取初始化向量（IV），然后使用该IV和预先定义的密钥
     * 初始化Cipher对象进行解密操作解密过程分为两个步骤：首先，从输入数据中分离出IV和密文；
     * 其次，使用分离出的IV和密钥初始化Cipher对象，并调用doFinal方法对密文进行解密
     *
     * @param data 要解密的密文，包括初始化向量和密文两部分
     * @return 解密后的明文
     * @throws RuntimeException 如果解密过程中发生任何错误，包括但不限于算法不可用、密钥无效等
     */
    public byte[] decrypt(byte[] data)  {
        try {
            // 初始化向量（IV）长度为16字节，从输入数据中提取
            byte[] iv = new byte[16];
            System.arraycopy(data, 0, iv, 0, 16);

            // 密文（ct）长度为输入数据长度减去IV长度，同样从输入数据中提取
            byte[] ct = new byte[data.length - 16];
            System.arraycopy(data, 16, ct, 0, ct.length);

            // 使用指定算法创建Cipher对象
            Cipher cipher = Cipher.getInstance(ALGORITHM);

            // 使用提取的IV创建IvParameterSpec对象，用于初始化Cipher对象
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            // 初始化Cipher对象为解密模式，使用预定义的密钥和提取的IV
            cipher.init(Cipher.DECRYPT_MODE, key, ivSpec);

            // 执行解密操作，并返回解密后的数据
            return cipher.doFinal(ct);
        } catch (Exception e) {
            // 如果发生任何异常，包装为RuntimeException并抛出
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成新的注册密钥内容
     * 该方法首先验证输入的服务器设备ID和字符串值是否为数字，然后将它们组合成一个字节数组，
     * 使用AES加密算法进行加密，并将加密后的结果与一个随机生成的初始化向量（IV）合并，
     * 最后将合并后的字节数组进行Base64编码并返回
     *
     * @param serverDeviceId 服务器设备ID，预期为数字字符串
     * @param strVal 字符串值，预期为数字字符串
     * @return 返回Base64编码的注册密钥内容
     * @throws RuntimeException 如果输入不符合要求或加密过程中出现错误，抛出运行时异常
     */
    public String newRegisterKeyContent(String serverDeviceId, String strVal) {
        try {
            // 验证输入是否为数字，如果不是，则抛出非法参数异常
            if (!serverDeviceId.matches("\\d+") || !strVal.matches("\\d+")) {
                throw new IllegalArgumentException("Parse failed\nserver_device_id: " + serverDeviceId + "\nstr_val:" + strVal);
            }
            // 将服务器设备ID和字符串值组合成一个字节数组
            byte[] combined = combineIntegersToByteArray(serverDeviceId, strVal);
            // 生成一个随机的初始化向量（IV），长度为16字节
            byte[] iv = generateRandomBytes(16);
            // 使用AES加密算法对组合后的字节数组进行加密
            byte[] encrypt = encrypt(combined, iv);
            // 将初始化向量和加密后的字节数组合并
            byte[] combinedBytes = concatenateByteArrays(iv, encrypt);

            // 对合并后的字节数组进行Base64编码并返回
            return Base64.encode(combinedBytes);
        } catch (Exception e) {
            // 如果出现异常，抛出运行时异常
            throw new RuntimeException(e);
        }
    }

    /**
     * 将十六进制字符串转换为字节数组
     *
     * @param s 十六进制字符串，例如 "1A3B"
     * @return 对应的字节数组
     *
     * 该方法解释了为什么需要将十六进制字符串转换为字节数组：
     * 在许多应用程序中，数据可能以十六进制字符串的形式接收或传输，
     * 但实际处理时需要将其转换回字节形式
     *
     * 方法选择使用位操作来转换每个十六进制字符对的原因：
     * 位操作是一种高效的方法，可以直接操作字节和位，
     * 对于将十六进制数字转换为字节特别有效，因为每个十六进制字符代表 4 位
     */
    private static byte[] hexStringToByteArray(String s) {
        // 计算字节数组长度，每个十六进制字符对代表一个字节
        int len = s.length();
        byte[] data = new byte[len / 2];

        // 遍历字符串，每两个字符转换为一个字节
        for (int i = 0; i < len; i += 2) {
            // 使用位操作将十六进制字符对转换为一个字节
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }

        // 返回转换后的字节数组
        return data;
    }

    /**
     * 将两个字符串表示的整数合并为一个字节数组
     * 此方法首先将每个字符串转换为长整型（Long），然后使用ByteBuffer将这两个长整型值转换为一个字节数组
     * 该字节数组按小端字节序（Little Endian）排列，常用于网络通信或数据存储中
     *
     * @param serverDeviceId 服务器设备ID的字符串表示，将被转换为长整型
     * @param strVal 另一个字符串值，同样会被转换为长整型
     * @return 包含两个长整型值的字节数组，长度为16
     */
    public static byte[] combineIntegersToByteArray(String serverDeviceId, String strVal) {
        // 将服务器设备ID的字符串表示转换为长整型
        long serverDeviceIdLong = Long.parseLong(serverDeviceId);
        // 将另一个字符串值转换为长整型
        long strValLong = Long.parseLong(strVal);

        // 创建一个长度为16的ByteBuffer，用于存储两个长整型值
        ByteBuffer buffer = ByteBuffer.allocate(16);
        // 设置ByteBuffer的字节序为小端字节序
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        // 将服务器设备ID的长整型值写入ByteBuffer
        buffer.putLong(serverDeviceIdLong);
        // 将另一个长整型值写入ByteBuffer
        buffer.putLong(strValLong);

        // 返回ByteBuffer中的字节数组，即合并后的结果
        return buffer.array();
    }


    public static byte[] generateRandomBytes(int length) {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[length];
        random.nextBytes(bytes);
        return bytes;
    }

    public static byte[] concatenateByteArrays(byte[] array1, byte[] array2) {
        ByteBuffer buffer = ByteBuffer.allocate(array1.length + array2.length);
        buffer.put(array1);
        buffer.put(array2);
        return buffer.array();
    }

}