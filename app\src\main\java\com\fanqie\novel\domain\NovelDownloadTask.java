package com.fanqie.novel.domain;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.fanqie.novel.constants.CommonCache;
import com.fanqie.novel.data.db.mapper.BookMapper;
import com.fanqie.novel.data.db.mapper.ChapterMapper;
import com.fanqie.novel.data.model.ChapterDownloadStats;
import com.fanqie.novel.data.model.ChapterInfo;
import com.fanqie.novel.service.NovelDownloadService;
import com.fanqie.novel.strategy.ChapterDownloadStrategy;
import com.fanqie.novel.strategy.DownloadCallback;
import com.fanqie.novel.strategy.impl.down.OpenSourceDownloadStrategy;
import com.fanqie.novel.strategy.impl.down.TomatoDownloadStrategy;
import com.fanqie.novel.ui.fragment.BookshelfFragment;
import com.fanqie.novel.util.DownUtil;
import com.fanqie.novel.util.DownloadConfigManager;
import com.fanqie.novel.util.LogUtil;
import com.fanqie.novel.util.ToastUtil;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Locale;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

public class NovelDownloadTask {
    private static final String TAG = "DownloadTask";
    private final String bookId;
    private static final int CORE_POOL_SIZE = 15;
    private static final int MAX_POOL_SIZE = 30;
    private static final int KEEP_ALIVE_TIME = 30;
    private static final int QUEUE_CAPACITY = 50;
    private static final int MAX_RETRY_COUNT = 3;
    private static final int UPDATE_PROGRESS_INTERVAL = 5; // 每下载5个章节更新一次进度
    private static final long RETRY_DELAY_MS = 1000;
    private static final long BATCH_TIMEOUT = 60;
    private final ExecutorService executorService;
    private final ChapterMapper chapterMapper;
    private final BookMapper bookMapper;
    private final NovelDownloadService downloadService;
    private final Handler mainHandler;
    private List<ChapterInfo> chaptersToDownload;
    private final ChapterDownloadStrategy strategy;

    public NovelDownloadTask(String bookId, NovelDownloadService downloadService) {
        this.bookId = bookId;
        this.downloadService = downloadService;
        this.executorService = new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(QUEUE_CAPACITY),
                new ThreadFactory() {
                    private final AtomicInteger counter = new AtomicInteger(1);

                    @Override
                    public Thread newThread(@NotNull Runnable r) {
                        Thread thread = new Thread(r);
                        thread.setName("DownloadThread-" + counter.getAndIncrement());
                        return thread;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy());
        this.chapterMapper = ChapterMapper.getInstance(downloadService);
        this.bookMapper = BookMapper.getInstance(downloadService);
        this.mainHandler = new Handler(Looper.getMainLooper());
        // 策略自动选择
        DownloadConfigManager configManager = DownloadConfigManager
                .getInstance(downloadService.getApplicationContext());
        switch (configManager.getCurrentStrategy()) {
            case PYTHON_LIKE:
                this.strategy = new OpenSourceDownloadStrategy();
                break;
            case LEGACY:
            default:
                this.strategy = new TomatoDownloadStrategy();
                break;
        }
        CommonCache.initBookNotificationId(bookId);
        Log.i(TAG, "Initialized download task for book: " + bookId);
    }

    public void execute() {
        try {
            showInitialNotification();
            Thread.sleep(100);
            LogUtil.setMdcContext(bookId);
            CompletableFuture.runAsync(this::doInBackground, executorService)
                    .whenComplete((result, throwable) -> {
                        if (throwable != null) {
                            LogUtil.e(TAG, "下载失败", throwable);
                        }
                        onPostExecute();
                        shutdownExecutor();
                    });
        } catch (Exception e) {
            LogUtil.e(TAG, "启动下载任务失败", e);
        } finally {
            LogUtil.clearMdcContext();
        }
    }

    private void doInBackground() {
        try {
            LogUtil.setMdcContext(bookId);
            // 先插入书籍信息到数据库
            LogUtil.i(TAG, "开始下载书籍内容: bookId=" + bookId);
            BookshelfFragment.sendRefreshBookshelfBroadcast(downloadService);
            List<ChapterInfo> chapters = DownUtil.getChapterList(bookId);
            if (CollUtil.isEmpty(chapters)) {
                LogUtil.e(TAG, "获取章节列表失败，章节数为0");
                return;
            }

            // 使用有效下载的章节ID列表（内容长度>500字符）
            List<String> validDownloadedChapterIds = chapterMapper.getValidDownloadedChapterIds(bookId);
            chaptersToDownload = chapters.stream()
                    .filter(chapter -> !validDownloadedChapterIds
                            .contains(StrUtil.subAfter(chapter.getLink(), "/", true)))
                    .collect(Collectors.toList());

            int totalChapters = chapters.size();
            int chaptersToDownloadCount = chaptersToDownload.size();
            int validDownloadedCount = validDownloadedChapterIds.size();
            AtomicInteger finished = new AtomicInteger(validDownloadedCount);

            LogUtil.i(TAG, String.format(Locale.getDefault(),
                    "总章节数: %d, 有效已下载: %d, 待下载: %d",
                    totalChapters, validDownloadedCount, chaptersToDownloadCount));

            updateDownloadProgress(finished.get(), totalChapters);

            if (chaptersToDownloadCount == 0) {
                onAllFinished();
                return;
            }

            if (strategy.supportsBatchDownload()) {
                batchDownloadChapter(finished, totalChapters);
            } else {
                SingleDownloadChapters(finished, totalChapters);
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "下载书籍失败: bookId=" + bookId, e);
        } finally {
            LogUtil.clearMdcContext();
        }
    }

    /**
     * 批量下载章节方法
     * 该方法负责将待下载的章节分成批次，并调用策略接口进行下载
     * 下载成功或失败后，会更新章节信息和下载进度
     *
     * @param finished      已完成下载的章节计数器
     * @param totalChapters 总章节数量
     */
    private void batchDownloadChapter(AtomicInteger finished, int totalChapters) {
        // 读取设置页面的批次数
        int batchSize = DownloadConfigManager.getInstance(downloadService.getApplicationContext()).getBatchSize();
        if (batchSize <= 0)
            batchSize = 250;
        // 将章节列表按照batchSize分割成多个子列表进行批量下载
        for (List<ChapterInfo> list : CollUtil.split(chaptersToDownload, batchSize)) {
            // 调用策略接口下载章节，传入下载回调接口处理下载结果
            strategy.downloadChapters(list, new DownloadCallback() {
                @Override
                public void onSuccess(ChapterInfo chapterInfo) {
                    chapterInfo.setBookId(bookId);
                    int contentLength = chapterInfo.getContent() != null ? chapterInfo.getContent().length() : 0;
                    chapterInfo.setCount(contentLength);

                    // 只有内容长度>500的章节才被认为是真正的成功
                    if (contentLength > 500) {
                        chapterInfo.setStatus(1);
                        int current = finished.incrementAndGet();
                        LogUtil.d(TAG, String.format("章节下载成功: %s, 内容长度: %d",
                                chapterInfo.getTitle(), contentLength));
                        updateDownloadProgress(current, totalChapters);
                        if (current == totalChapters)
                            onAllFinished();
                    } else {
                        chapterInfo.setStatus(0);
                        LogUtil.w(TAG, String.format("章节内容过短，标记为失败: %s, 内容长度: %d",
                                chapterInfo.getTitle(), contentLength));
                        // 内容过短的章节不增加成功计数，但仍需检查是否所有章节都处理完毕
                        checkAllChaptersProcessed(totalChapters);
                    }
                    chapterMapper.insertChapter(chapterInfo);
                }

                @Override
                public void onFailure(ChapterInfo chapterInfo, Throwable error) {
                    if (chapterInfo == null)
                        chapterInfo = new ChapterInfo();
                    chapterInfo.setBookId(bookId);
                    chapterInfo.setStatus(0);
                    chapterInfo.setCount(0);
                    chapterMapper.insertChapter(chapterInfo);
                    LogUtil.w(TAG, String.format("章节下载失败: %s, 错误: %s",
                            chapterInfo.getTitle(), error.getMessage()));
                    // 失败的章节不增加成功计数，但仍需检查是否所有章节都处理完毕
                    checkAllChaptersProcessed(totalChapters);
                }
            });
        }
    }

    /**
     * 单线程下载章节方法
     *
     * @param finished      已完成下载的章节计数器
     * @param totalChapters 总章节数
     */
    private void SingleDownloadChapters(AtomicInteger finished, int totalChapters) {
        // 遍历待下载的章节列表
        for (ChapterInfo chapter : chaptersToDownload) {
            // 调用策略对象的下载章节方法
            strategy.downloadChapter(
                    chapter,
                    bookId,
                    new DownloadCallback() {
                        @Override
                        public void onSuccess(ChapterInfo info) {
                            int contentLength = info.getContent() != null ? info.getContent().length() : 0;
                            info.setCount(contentLength);

                            // 只有内容长度>500的章节才被认为是真正的成功
                            if (contentLength > 500) {
                                info.setStatus(1);
                                int current = finished.incrementAndGet();
                                LogUtil.d(TAG, String.format("章节下载成功: %s, 内容长度: %d",
                                        info.getTitle(), contentLength));
                                updateDownloadProgress(current, totalChapters);
                                if (current == totalChapters)
                                    onAllFinished();
                            } else {
                                info.setStatus(0);
                                LogUtil.w(TAG, String.format("章节内容过短，标记为失败: %s, 内容长度: %d",
                                        info.getTitle(), contentLength));
                                // 内容过短的章节不增加成功计数，但仍需检查是否所有章节都处理完毕
                                checkAllChaptersProcessed(totalChapters);
                            }
                            chapterMapper.insertChapter(info);
                        }

                        @Override
                        public void onFailure(ChapterInfo info, Throwable error) {
                            if (info == null)
                                info = new ChapterInfo();
                            info.setStatus(0);
                            info.setCount(0);
                            chapterMapper.insertChapter(info);
                            LogUtil.w(TAG, String.format("章节下载失败: %s, 错误: %s",
                                    info.getTitle(), error.getMessage()));
                            // 失败的章节不增加成功计数，但仍需检查是否所有章节都处理完毕
                            checkAllChaptersProcessed(totalChapters);
                        }
                    });
        }
    }

    /**
     * 检查是否所有章节都已处理完毕（无论成功还是失败）
     * 当章节内容过短或下载失败时调用此方法
     */
    private void checkAllChaptersProcessed(int totalChapters) {
        // 获取当前的下载统计信息
        ChapterDownloadStats stats = chapterMapper.getChapterDownloadStats(bookId);

        // 如果已处理的章节数（成功+失败）等于总章节数，说明下载任务完成
        int processedChapters = stats.validDownloads + stats.failedDownloads;
        if (processedChapters >= totalChapters) {
            LogUtil.i(TAG, String.format("所有章节处理完毕: bookId=%s, 成功: %d, 失败: %d, 总数: %d",
                    bookId, stats.validDownloads, stats.failedDownloads, totalChapters));
            onAllFinished();
        }
    }

    private void onAllFinished() {
        // 获取章节下载统计信息
        ChapterDownloadStats stats = chapterMapper.getChapterDownloadStats(bookId);

        String bookName = CommonCache.getBookName(bookId);
        String toastMessage;

        if (stats.isAllDownloaded()) {
            // 所有章节都下载成功
            bookMapper.updateStatusByBookId(bookId, 1);
            toastMessage = "《" + bookName + "》下载完成";
            LogUtil.i(TAG, String.format("书籍下载完成: bookId=%s, 成功章节数: %d/%d",
                    bookId, stats.validDownloads, stats.totalChapters));
        } else {
            // 存在下载失败的章节，保持未下载状态
            bookMapper.updateStatusByBookId(bookId, 0);
            toastMessage = String.format("《%s》下载完成，部分章节失败 (%s)",
                    bookName, stats.getProgressString());
            LogUtil.w(TAG, String.format("书籍下载部分失败: bookId=%s, 成功章节数: %d/%d, 失败章节数: %d",
                    bookId, stats.validDownloads, stats.totalChapters, stats.failedDownloads));
        }

        mainHandler.post(() -> {
            showToast(toastMessage);
            // 发送下载完成广播，由BookshelfFragment统一处理通知和进度条
            try {
                BookshelfFragment.sendDownloadCompleteBroadcast(downloadService, bookId);
            } catch (Exception e) {
                LogUtil.e(TAG, "发送下载完成广播失败", e);
            }
        });
    }

    private void onPostExecute() {
        mainHandler.post(() -> {
            // 停止前台服务，但保留通知
            downloadService.stopForeground(false);

            // 使用自定义Toast显示
            if (chaptersToDownload != null && !chaptersToDownload.isEmpty()) {
                showToast("《" + CommonCache.getBookName(bookId) + "》下载完成");
            }
        });
    }

    private void shutdownExecutor() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    public String getBookId() {
        return bookId;
    }

    // 添加显示初始通知的方法
    private void showInitialNotification() {
        try {
            LogUtil.d(TAG, "启动前台服务");
            NotificationCompat.Builder builder = downloadService.getBuilder()
                    .setSmallIcon(android.R.drawable.stat_sys_download)
                    .setContentTitle(CommonCache.getBookName(bookId))
                    .setContentText("准备下载中...")
                    .setProgress(0, 0, true)
                    .setOngoing(true)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setAutoCancel(false);

            // 确保在主线程中启动前台服务
            mainHandler.post(() -> {
                try {
                    // 仅使用startForeground启动前台服务
                    downloadService.startForeground(CommonCache.getBookNotificationId(bookId), builder.build());

                    // 立即发送初始进度广播，让BookshelfFragment负责显示通知
                    BookshelfFragment.sendDownloadProgressBroadcast(
                            downloadService,
                            bookId,
                            0, // 初始进度为0
                            0, // 已处理章节为0
                            100 // 假设有100章，后续会更新
                    );

                    LogUtil.d(TAG, "前台服务启动成功: bookId=" + bookId);
                } catch (Exception e) {
                    LogUtil.e(TAG, "启动前台服务失败", e);
                }
            });
        } catch (Exception e) {
            LogUtil.e(TAG, "创建前台服务通知失败", e);
        }
    }

    private void showToast(String message) {
        try {
            LogUtil.d(TAG, "准备显示Toast消息: " + message);
            mainHandler.post(() -> {
                try {
                    // 使用showSuccessToast方法，显示时间更长
                    ToastUtil.showSuccessToast(downloadService.getApplicationContext(), message);
                    LogUtil.d(TAG, "Toast消息显示成功: " + message);
                } catch (Exception e) {
                    LogUtil.e(TAG, "显示Toast失败: " + message, e);
                }
            });
        } catch (Exception e) {
            LogUtil.e(TAG, "Toast处理异常: " + message, e);
        }
    }

    /**
     * 更新下载进度
     */
    private void updateDownloadProgress(int processedChapters, int totalChapters) {
        if (totalChapters <= 0)
            return;

        int progress = (int) ((processedChapters * 100.0f) / totalChapters);
        // 发送广播通知BookshelfFragment更新UI和通知
        BookshelfFragment.sendDownloadProgressBroadcast(
                downloadService,
                bookId,
                progress,
                processedChapters,
                totalChapters);
    }
}