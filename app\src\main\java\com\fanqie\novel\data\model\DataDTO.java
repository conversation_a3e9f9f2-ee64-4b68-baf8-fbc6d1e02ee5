package com.fanqie.novel.data.model;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/16 18:16
 * @description DataDTO
 */
public class DataDTO {
    @JSONField(name = "book_data")
    private List<BookDataDTO> bookData;

    public List<BookDataDTO> getBookData() {
        return bookData;
    }

    public void setBookData(List<BookDataDTO> bookData) {
        this.bookData = bookData;
    }
}
