package com.fanqie.novel.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.Looper;
import android.widget.ImageView;
import com.fanqie.novel.R;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 简单的图片加载工具类
 */
public class ImageLoader {
    private static final ExecutorService executor = Executors.newFixedThreadPool(5);
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    /**
     * 加载图片
     * @param context 上下文
     * @param imageUrl 图片URL
     * @param imageView 要显示图片的ImageView
     */
    public static void loadImage(Context context, String imageUrl, ImageView imageView) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            imageView.setImageResource(R.drawable.ic_bookshelf);
            return;
        }

        // 设置加载占位图
        imageView.setImageResource(R.drawable.ic_bookshelf);

        // 异步加载图片
        executor.execute(() -> {
            Bitmap bitmap = null;
            
            // 尝试从网络加载图片
            try {
                URL url = new URL(imageUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setDoInput(true);
                connection.connect();
                InputStream input = connection.getInputStream();
                bitmap = BitmapFactory.decodeStream(input);
                input.close();
                connection.disconnect();
            } catch (IOException e) {
                LogUtil.e("ImageLoader", "加载图片失败: " + imageUrl, e);
            }

            // 如果加载失败，使用默认图片
            final Bitmap finalBitmap = bitmap != null ? bitmap : 
                    BitmapFactory.decodeResource(context.getResources(), R.drawable.ic_bookshelf);

            // 在主线程设置图片
            mainHandler.post(() -> {
                if (imageView != null) {
                    imageView.setImageBitmap(finalBitmap);
                }
            });
        });
    }
} 