package com.fanqie.novel.data.model;

/**
 * API状态跟踪数据模型
 * 用于记录每个API端点的性能和状态信息
 */
public class ApiStatus {
    private volatile double lastResponseTime = Double.MAX_VALUE;
    private volatile int errorCount = 0;
    private volatile long lastTryTime = 0;
    private volatile int successCount = 0;
    private volatile double averageResponseTime = Double.MAX_VALUE;

    /**
     * 更新成功状态
     * @param responseTime 响应时间（秒）
     */
    public synchronized void updateOnSuccess(double responseTime) {
        this.lastResponseTime = responseTime;
        this.errorCount = Math.max(0, this.errorCount - 1);
        this.successCount++;
        // 计算平均响应时间
        if (this.averageResponseTime == Double.MAX_VALUE) {
            this.averageResponseTime = responseTime;
        } else {
            this.averageResponseTime = (this.averageResponseTime * 0.7) + (responseTime * 0.3);
        }
    }

    /**
     * 更新错误状态
     */
    public synchronized void updateOnError() {
        this.errorCount++;
    }

    /**
     * 更新最后尝试时间
     */
    public synchronized void updateLastTryTime() {
        this.lastTryTime = System.currentTimeMillis();
    }

    /**
     * 计算API优先级分数（越小越优先）
     * @return 优先级分数
     */
    public synchronized double getPriorityScore() {
        double errorPenalty = errorCount * 2.0;
        double timePenalty = (averageResponseTime == Double.MAX_VALUE) ? 10.0 : averageResponseTime;
        double successBonus = successCount > 0 ? -Math.log(successCount + 1) : 0;
        return errorPenalty + timePenalty + successBonus;
    }

    // Getters
    public double getLastResponseTime() {
        return lastResponseTime;
    }

    public int getErrorCount() {
        return errorCount;
    }

    public long getLastTryTime() {
        return lastTryTime;
    }

    public int getSuccessCount() {
        return successCount;
    }

    public double getAverageResponseTime() {
        return averageResponseTime;
    }
}
