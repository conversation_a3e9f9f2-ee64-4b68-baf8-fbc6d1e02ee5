package com.fanqie.novel.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fanqie.novel.data.model.ChapterInfo;
import com.fanqie.novel.data.model.TomatoVariable;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.GZIPInputStream;

public class TomatoRequest {
    private static String key = "654a8a07d536bdf619a478f72ba0d93b";
    private final TomatoVariable var;

    public TomatoRequest(TomatoVariable var) {
        this.var = var;
    }

    public JSONObject batchGet(String itemIds) {
        String url = "https://api5-normal-sinfonlineb.fqnovel.com/reading/reader/batch_full/v";
        HttpRequest request = HttpUtil.createGet(url)
                .form("item_ids", itemIds)
                .form("req_type", "1")
                .form("aid", var.getAid())
                .form("update_version_code", var.getUpdateVersionCode())
                .header("Cookie", StrUtil.format("install_id={}", var.getInstallId()));

        try (HttpResponse response = request.execute()) {
            if (!response.isOk()) {
                throw new RuntimeException("HTTP error: 可能是配置不可用，修改配置");
            }
            return JSONUtil.parseObj(response.body());
        }

    }

    public String getRegisterKey() {
        String url = "https://api5-normal-sinfonlineb.fqnovel.com/reading/crypt/registerkey?aid=" + var.getAid();

        // 生成加密内容
        TomatoCrypt crypto = new TomatoCrypt(grk());  // 假设grk()已实现

        // 构建请求体
        Map<String, Object> payload = new HashMap<String, Object>() {{
            put("content", crypto.newRegisterKeyContent(String.valueOf(var.getServerDeviceId()), "0"));
            put("keyver", 1);
        }};

        HttpRequest request = HttpRequest.post(url)
                .body(JSONUtil.toJsonStr(payload), "application/json")
                .header("Content-Type", "application/json")
                .header("Cookie", StrUtil.format("install_id={}", var.getInstallId()));

        try (HttpResponse response = request.execute()) {
            if (!response.isOk()) {
                throw new RuntimeException("HTTP error: 可能是配置不可用，修改配置");
            }
            JSONObject responseBody = JSONUtil.parseObj(response.body());

            String key = JSONUtil.getByPath(responseBody, "data.key").toString();
            return HexUtil.encodeHexStr(crypto.decrypt(cn.hutool.core.codec.Base64.decode(key)));
        }

    }

    private String grk() {
        return "ac25c67ddd8f38c1b37a2348828e222e";
    }

    public ChapterInfo getDecryptContents(JSONObject resArr, String itemId) {
        int retryCount = 0;
        do {
            try {
                ChapterInfo chapterInfo = new ChapterInfo();
                chapterInfo.setId(itemId);
                chapterInfo.setTitle(resArr.getByPath("data." + itemId + ".title", String.class));
                TomatoCrypt crypto = new TomatoCrypt(key);
                JSONObject content = resArr.getByPath("data." + itemId, JSONObject.class);
                byte[] encrypted = Base64.decode(content.getStr("content"));
                byte[] decrypted = crypto.decrypt(encrypted);
                chapterInfo.setContent(ContentCleaner.processContent(decompressGzip(decrypted)));
                chapterInfo.setCount(chapterInfo.getContent().length());
                return chapterInfo;
            } catch (Exception e) {
                retryCount++;
                ThreadUtil.sleep(500);
                key = getRegisterKey();
            }
        } while (retryCount <= 3);
        return null;
    }

    private String decompressGzip(byte[] compressed) throws IOException {
        try (ByteArrayInputStream bis = new ByteArrayInputStream(compressed);
             GZIPInputStream gis = new GZIPInputStream(bis);
             BufferedReader br = new BufferedReader(new InputStreamReader(gis))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();
        }
    }
}