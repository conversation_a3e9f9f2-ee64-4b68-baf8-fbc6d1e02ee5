<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="12dp">

    <TextView
        android:id="@+id/tv_paragraph"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="8dp"
        android:lineSpacingMultiplier="1.2"
        android:textColor="@color/reader_text_default"
        android:textSize="18sp"
        android:textIsSelectable="true"
        android:autoLink="none"
        android:hyphenationFrequency="normal"
        android:breakStrategy="high_quality"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="这是段落内容，小说中的一个段落的文本。在古风流传的时候，许多故事在不同的时代流传开来，成为了千古佳话。" />

</androidx.constraintlayout.widget.ConstraintLayout> 