<!-- res/layout/toolbar_layout.xml -->
<androidx.appcompat.widget.Toolbar
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/purple_66b"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
        app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textSize="20sp"
                android:textColor="@color/white"
                android:gravity="center"
                android:layout_centerInParent="true"/>

        <ImageButton
                android:id="@+id/btn_settings"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_settings"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="设置"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:padding="12dp"/>
    </RelativeLayout>

</androidx.appcompat.widget.Toolbar>