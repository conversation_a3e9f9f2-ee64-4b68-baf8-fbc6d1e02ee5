package com.fanqie.novel.strategy.impl.down;

import android.os.Handler;
import android.os.Looper;

import com.fanqie.novel.constants.DownloadStrategyType;
import com.fanqie.novel.data.model.ApiEndpoint;
import com.fanqie.novel.data.model.ApiStatus;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.data.model.ChapterInfo;
import com.fanqie.novel.domain.BatchConfig;
import com.fanqie.novel.strategy.ChapterDownloadStrategy;
import com.fanqie.novel.strategy.DownloadCallback;
import com.fanqie.novel.util.ApiRequestUtil;
import com.fanqie.novel.util.ApiStatusManager;
import com.fanqie.novel.util.DownloadConfigManager;
import com.fanqie.novel.util.HtmlUtils;
import com.fanqie.novel.util.LogUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

public class OpenSourceDownloadStrategy implements ChapterDownloadStrategy {

    private static final Random random = new Random();

    @Override
    public void downloadChapter(ChapterInfo chapter, String bookId, DownloadCallback callback) {
        List<ApiEndpoint> endpoints = DownloadConfigManager.getInstance(null).getApiEndpoints();
        if (endpoints == null || endpoints.isEmpty()) {
            LogUtil.e("PythonLikeDownloadStrategy", "API端点未配置，请在设置中获取API列表");
            callback.onFailure(chapter, new Exception("API端点未配置，请在设置中获取API列表"));
            return;
        }

        // 初始化API状态跟踪
        ApiStatusManager.initializeApiStatus(endpoints);

        String userAgent = getRandomUserAgent();
        LogUtil.d("PythonLikeDownloadStrategy",
                "down_text风格下载章节: " + chapter.getId() + ", 尝试API端点数: " + endpoints.size());

        new Thread(() -> {
            boolean downloaded = false;
            boolean firstErrorPrinted = false;

            // 根据历史性能对API端点进行智能排序
            List<ApiEndpoint> sortedEndpoints = ApiStatusManager.getSortedEndpointsByPerformance(endpoints);

            for (int idx = 0; idx < sortedEndpoints.size(); idx++) {
                ApiEndpoint endpoint = sortedEndpoints.get(idx);
                String apiName = endpoint.getName();
                String baseUrl = endpoint.getUrl();

                // 更新尝试时间
                ApiStatus status = ApiStatusManager.getApiStatus(baseUrl);
                if (status != null) {
                    status.updateLastTryTime();
                }

                try {
                    // 随机延迟以避免频繁请求
                    Thread.sleep((long) (random.nextDouble() * 400 + 100)); // 0.1-0.5秒随机延迟

                    long startTime = System.currentTimeMillis();
                    String url = baseUrl.replace("{chapter_id}", chapter.getId());

                    LogUtil.d("PythonLikeDownloadStrategy", "请求API: " + url + ", name=" + apiName +
                            ", 错误计数: " + (status != null ? status.getErrorCount() : 0) +
                            ", 平均响应时间: "
                            + (status != null ? ApiRequestUtil.formatResponseTime(status.getAverageResponseTime())
                                    : "未知"));

                    boolean success = tryDownloadFromEndpoint(endpoint, chapter, userAgent, startTime);

                    if (success) {
                        downloaded = true;
                        LogUtil.i("PythonLikeDownloadStrategy", "章节下载成功: " + apiName +
                                ", 响应时间: " + (System.currentTimeMillis() - startTime) + "ms");
                        new Handler(Looper.getMainLooper()).post(() -> callback.onSuccess(chapter));
                        break;
                    } else {
                        // 记录失败
                        if (status != null) {
                            status.updateOnError();
                        }

                        if (!firstErrorPrinted) {
                            LogUtil.w("PythonLikeDownloadStrategy", "API：" + apiName + " 错误，无法下载章节，正在重试");
                            firstErrorPrinted = true;
                        }
                    }

                } catch (Exception e) {
                    // 记录异常
                    if (status != null) {
                        status.updateOnError();
                    }

                    if (!firstErrorPrinted) {
                        LogUtil.w("PythonLikeDownloadStrategy",
                                "API：" + apiName + " 异常，无法下载章节，正在重试: " + e.getMessage());
                        firstErrorPrinted = true;
                    }

                    // 异常时等待更长时间
                    try {
                        Thread.sleep(3000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }

                // 切换API提示
                if (idx < sortedEndpoints.size() - 1) {
                    LogUtil.d("PythonLikeDownloadStrategy", "正在切换到下一个API");
                }
            }

            if (!downloaded) {
                LogUtil.e("PythonLikeDownloadStrategy", "所有API尝试失败，无法下载章节: " + chapter.getId());
                new Handler(Looper.getMainLooper())
                        .post(() -> callback.onFailure(chapter, new Exception("所有API尝试失败，无法下载章节 " + chapter.getId())));
            }
        }).start();
    }

    /**
     * 尝试从指定端点下载章节内容
     */
    private boolean tryDownloadFromEndpoint(ApiEndpoint endpoint, ChapterInfo chapter, String userAgent,
            long startTime) {
        String apiName = endpoint.getName();
        String chapterId = chapter.getId();

        try {
            HttpResponse response;

            // 根据API类型选择不同的请求方式
            if ("fanqie_sdk".equals(apiName)) {
                response = ApiRequestUtil.makeFanqieSdkRequest(endpoint, chapterId, userAgent);
            } else {
                response = ApiRequestUtil.makeStandardGetRequest(endpoint, chapterId, userAgent);
            }

            if (response == null) {
                return false;
            }

            try (response) {
                int httpCode = response.getStatus();
                double responseTime = (System.currentTimeMillis() - startTime) / 1000.0;

                if (httpCode == 200) {
                    String body = response.body();
                    JSON json = JSONUtil.parse(body);

                    // 根据不同API类型处理响应
                    String content = null;
                    String title = null;

                    if ("fanqie_sdk".equals(apiName)) {
                        content = JSONUtil.getByPath(json, "data.content", "");
                        title = JSONUtil.getByPath(json, "data.title", "");
                    } else if ("fqweb".equals(apiName)) {
                        Integer dataCode = JSONUtil.getByPath(json, "data.code", 1);
                        if (dataCode != null && dataCode == 0) {
                            content = JSONUtil.getByPath(json, "data.data.content", "");
                            title = JSONUtil.getByPath(json, "data.data.title", "");
                        }
                    } else {
                        // 通用处理
                        Integer code = JSONUtil.getByPath(json, "code", 1);
                        if (code != null && code == 0) {
                            content = JSONUtil.getByPath(json, "data.content", "");
                            title = JSONUtil.getByPath(json, "data.title", "");
                        }
                    }

                    if (!StrUtil.isBlank(content)) {
                        // 更新成功状态
                        ApiStatus status = ApiStatusManager.getApiStatus(endpoint.getUrl());
                        if (status != null) {
                            status.updateOnSuccess(responseTime);
                        }

                        String processed = HtmlUtils.cleanHtml(content);
                        if (!StrUtil.isBlank(title)) {
                            chapter.setTitle(title);
                        }
                        chapter.setContent(processed);
                        chapter.setCount(processed.length());
                        return true;
                    } else {
                        LogUtil.w("PythonLikeDownloadStrategy", "API章节内容为空: " + endpoint.getUrl());
                    }
                } else {
                    LogUtil.w("PythonLikeDownloadStrategy", "API响应非200: " + endpoint.getUrl() + ", code=" + httpCode);
                }
            }
        } catch (Exception e) {
            LogUtil.e("PythonLikeDownloadStrategy", "API请求异常: " + endpoint.getUrl() + ", error: " + e.getMessage());
        }

        return false;
    }

    private void postFailure(DownloadCallback callback, Throwable error) {
        new Handler(Looper.getMainLooper()).post(() -> callback.onFailure(null, error));
    }

    private String getRandomUserAgent() {
        String[] chromeAgents = {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        };
        String[] edgeAgents = {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/119.0.0.0 Safari/537.36"
        };

        // 随机选择浏览器类型
        boolean useChrome = random.nextBoolean();
        String[] agents = useChrome ? chromeAgents : edgeAgents;
        return agents[random.nextInt(agents.length)];
    }

    /**
     * 拉取API端点配置并缓存到DownloadConfigManager
     *
     * @param serverUrl 服务器地址
     * @param authToken 认证token
     * @param onResult  结果回调（主线程）
     */
    public void fetchApiEndpointsFromServer(String serverUrl, String authToken, OnFetchResult onResult) {
        new Thread(() -> {
            List<ApiEndpoint> endpoints = new ArrayList<>();
            boolean success = false;
            String errorMsg = null;
            BatchConfig batchConfig = new BatchConfig();
            String batchApiName = DownloadConfigManager.getInstance(null).getName();
            try {
                LogUtil.i("PythonLikeDownloadStrategy", "拉取API端点配置: " + serverUrl);
                try (HttpResponse response = HttpRequest.get(serverUrl)
                        .header("X-Auth-Token", authToken)
                        .header("Accept", "application/json")
                        .timeout(10000)
                        .execute()) {
                    int code = response.getStatus();
                    if (code == 200) {
                        String body = response.body();
                        JSONObject json = new JSONObject(body);
                        JSONArray sources = json.getJSONArray("sources");

                        if (sources != null) {
                            for (int i = 0; i < sources.size(); i++) {
                                JSONObject src = sources.getJSONObject(i);
                                if (src.getBool("enabled", false)) {
                                    String name = src.getStr("name", "");
                                    String singleUrl = src.getStr("single_url", "");
                                    String token = src.getStr("token", "");
                                    if (name.equals(batchApiName)) {
                                        // 批量下载配置
                                        String baseUrl = singleUrl.split("\\?")[0];
                                        String batchEndpoint = baseUrl.substring(baseUrl.lastIndexOf("/") + 1);
                                        baseUrl = baseUrl.substring(0, baseUrl.lastIndexOf("/"));
                                        batchConfig.setBaseUrl(baseUrl);
                                        batchConfig.setBatchEndpoint("/" + batchEndpoint);
                                        batchConfig.setToken(token);
                                        batchConfig.setMaxBatchSize(290);
                                        batchConfig.setTimeout(10);
                                        batchConfig.setEnabled(true);
                                        // 新增：将批量下载api也加入endpoints，便于设置页面展示
                                        String batchApiUrl = baseUrl + "/" + batchEndpoint;
                                        endpoints.add(new ApiEndpoint(batchApiUrl, "批量下载API", token, null, null));
                                    } else {
                                        // 单章下载
                                        if (!StrUtil.isEmpty(singleUrl)) {
                                            // 检查是否有params和data字段（用于fanqie_sdk等特殊API）
                                            Map<String, Object> params = null;
                                            Map<String, Object> data = null;

                                            if (src.containsKey("params")) {
                                                JSONObject paramsJson = src.getJSONObject("params");
                                                if (paramsJson != null) {
                                                    params = paramsJson.toBean(Map.class);
                                                }
                                            }

                                            if (src.containsKey("data")) {
                                                JSONObject dataJson = src.getJSONObject("data");
                                                if (dataJson != null) {
                                                    data = dataJson.toBean(Map.class);
                                                }
                                            }

                                            endpoints.add(new ApiEndpoint(singleUrl, name, token, params, data));
                                        }
                                    }
                                }
                            }
                            success = true;
                            // 缓存到全局配置
                            DownloadConfigManager.getInstance(null).setBatchConfig(batchConfig);
                            DownloadConfigManager.getInstance(null).setApiEndpoints(endpoints);
                            LogUtil.i("PythonLikeDownloadStrategy", "拉取API端点配置成功，缓存信息: " + DownloadConfigManager
                                    .getInstance(null).getConfig(DownloadStrategyType.PYTHON_LIKE));
                        } else {
                            errorMsg = "sources字段为空";
                        }
                    } else {
                        errorMsg = "HTTP错误码:" + code;
                    }
                }
            } catch (Exception e) {
                errorMsg = e.getMessage();
            }
            boolean finalSuccess = success;
            String finalErrorMsg = errorMsg;
            new Handler(Looper.getMainLooper()).post(() -> onResult.onResult(finalSuccess, endpoints, finalErrorMsg));
        }).start();
    }

    public interface OnFetchResult {
        void onResult(boolean success, List<ApiEndpoint> endpoints, String errorMsg);
    }

    @Override
    public void getBookInfo(String bookId, BookInfoCallback callback) {
        BookInfo cached = DownloadConfigManager.getInstance(null).getBookInfoById(bookId);
        if (cached != null && cached.getBookName() != null) {
            callback.onSuccess(cached);
            return;
        }
        // 如无缓存，可实现API端点拉取（此处简化为失败）
        callback.onFailure(new Exception("未获取到书籍信息（请先在详情页或书架页加载/缓存）"));
    }

    public void batchDownloadChapters(List<ChapterInfo> chapterList, DownloadCallback callback) {
        BatchConfig batchConfig = DownloadConfigManager.getInstance(null).getBatchConfig();
        // 优先用设置页面的批次数
        int userBatchSize = DownloadConfigManager.getInstance(null).getBatchSize();
        if (userBatchSize > 0) {
            batchConfig.setMaxBatchSize(userBatchSize);
        }
        if (batchConfig == null || !batchConfig.isEnabled()) {
            LogUtil.e("PythonLikeDownloadStrategy", "批量下载功能未启用");
            postFailure(callback, new Exception("批量下载功能未启用"));
            return;
        }
        String url = batchConfig.getBaseUrl() + batchConfig.getBatchEndpoint();
        List<String> itemIds = chapterList.stream().map(ChapterInfo::getId).collect(Collectors.toList());
        JSONObject payload = new JSONObject();
        payload.set("item_ids", itemIds);
        try {
            LogUtil.d("PythonLikeDownloadStrategy", "批量下载POST: " + url + ", itemIds: " + itemIds.size());
            HttpRequest req = HttpUtil.createPost(url)
                    .header("Content-Type", "application/json")
                    .header("Accept", "application/json")
                    .header("Accept-Language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7")
                    .header("Referer", "https://fanqienovel.com/")
                    .header("X-Requested-With", "XMLHttpRequest")
                    .body(payload.toString());
            if (batchConfig.getToken() != null && !batchConfig.getToken().isEmpty()) {
                req.header("token", batchConfig.getToken());
            }
            try (HttpResponse response = req.execute()) {
                if (response.getStatus() == 200) {
                    JSONObject json = new JSONObject(response.body());
                    int respCode = json.getInt("code", 1);
                    if (respCode == 0) {
                        for (ChapterInfo chapter : chapterList) {
                            String chapterId = chapter.getId();
                            String content = json.getByPath("data." + chapterId + ".content", String.class);
                            Integer code = json.getByPath("data." + chapterId + ".code", Integer.class);
                            String title = json.getByPath("data." + chapterId + ".title", String.class);
                            if (code == 0 && !StrUtil.isBlank(content)) {
                                String processed = HtmlUtils.cleanHtml(content);
                                chapter.setTitle(title);
                                chapter.setContent(processed);
                                chapter.setCount(processed.length());
                                new Handler(Looper.getMainLooper()).post(() -> callback.onSuccess(chapter));
                            } else {
                                new Handler(Looper.getMainLooper()).post(() -> callback.onFailure(chapter,
                                        new Exception("章节内容为空或code!=0: " + chapterId)));
                            }
                        }
                    } else {
                        LogUtil.e("PythonLikeDownloadStrategy", "批量下载返回code!=0: " + respCode);
                        postFailure(callback, new Exception("批量下载返回code!=0: " + respCode));
                    }
                } else {
                    LogUtil.e("PythonLikeDownloadStrategy", "批量下载HTTP错误: " + response.body());
                    postFailure(callback, new Exception("批量下载HTTP错误: " + response.body()));
                }
            }
        } catch (Exception e) {
            LogUtil.e("PythonLikeDownloadStrategy", "批量下载异常: " + e.getMessage());
            postFailure(callback, e);
        }
    }

    @Override
    public void downloadChapters(List<ChapterInfo> chapters, DownloadCallback callback) {
        BatchConfig batchConfig = DownloadConfigManager.getInstance(null).getBatchConfig();
        LogUtil.d("PythonLikeDownloadStrategy",
                "批量下载请求，章节数: " + chapters.size() + ", 批量配置: " + (batchConfig != null && batchConfig.isEnabled()));
        if (batchConfig != null && batchConfig.isEnabled()) {
            // 批量下载
            batchDownloadChapters(chapters, new DownloadCallback() {
                @Override
                public void onSuccess(ChapterInfo chapter) {
                    LogUtil.i("PythonLikeDownloadStrategy", "批量下载成功: 标题=" + chapter.getTitle() + ", 内容长度="
                            + (chapter.getContent() != null ? chapter.getContent().length() : 0));
                    callback.onSuccess(chapter);
                }

                @Override
                public void onFailure(ChapterInfo chapter, Throwable error) {
                    LogUtil.e("PythonLikeDownloadStrategy", "批量下载失败，降级为单章: " + error.getMessage());
                    downloadChaptersSingle(chapters, callback);
                }
            });
        } else {
            // 无批量配置，直接单章下载
            LogUtil.d("PythonLikeDownloadStrategy", "无批量配置，循环单章下载");
            downloadChaptersSingle(chapters, callback);
        }
    }

    private void downloadChaptersSingle(List<ChapterInfo> chapters, DownloadCallback callback) {
        LogUtil.d("PythonLikeDownloadStrategy", "循环单章下载，章节数: " + chapters.size());
        for (ChapterInfo chapter : chapters) {
            downloadChapter(chapter, null, callback);
        }
    }

    @Override
    public boolean supportsBatchDownload() {
        BatchConfig batchConfig = DownloadConfigManager.getInstance(null).getBatchConfig();
        return batchConfig != null && batchConfig.isEnabled();
    }

}
