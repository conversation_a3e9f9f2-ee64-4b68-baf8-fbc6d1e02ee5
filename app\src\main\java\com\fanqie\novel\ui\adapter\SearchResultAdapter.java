package com.fanqie.novel.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.fanqie.novel.R;
import com.fanqie.novel.data.model.BookInfo;
import com.squareup.picasso.Picasso;

import java.util.List;

public class SearchResultAdapter extends RecyclerView.Adapter<SearchResultAdapter.SearchViewHolder> {

    private final List<BookInfo> books;
    private final OnBookClickListener listener;

    public SearchResultAdapter(List<BookInfo> books, OnBookClickListener listener) {
        this.books = books;
        this.listener = listener;
    }

    @NonNull
    @Override
    public SearchViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_search_result, parent, false);
        return new SearchViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SearchViewHolder holder, int position) {
        BookInfo book = books.get(position);
        holder.bind(book, listener);
    }

    @Override
    public int getItemCount() {
        return books.size();
    }

    public static class SearchViewHolder extends RecyclerView.ViewHolder {
        private final ImageView coverImageView;
        private final TextView titleTextView;
        private final TextView authorTextView;
        private final TextView scoreTextView;
        private final TextView descriptionTextView;

        public SearchViewHolder(@NonNull View itemView) {
            super(itemView);
            coverImageView = itemView.findViewById(R.id.book_cover);
            titleTextView = itemView.findViewById(R.id.book_title);
            authorTextView = itemView.findViewById(R.id.book_author);
            scoreTextView = itemView.findViewById(R.id.book_score);
            descriptionTextView = itemView.findViewById(R.id.book_description);
        }

        public void bind(final BookInfo book, final OnBookClickListener listener) {
            titleTextView.setText(book.getBookName());
            authorTextView.setText("作者：" + book.getAuthor());
            scoreTextView.setText("分数：" + book.getCount());
            
            String description = book.getAbstractContent();
            if (description != null && !description.isEmpty()) {
                descriptionTextView.setText(description);
                descriptionTextView.setVisibility(View.VISIBLE);
            } else {
                descriptionTextView.setVisibility(View.GONE);
            }

            // 加载图片，使用Picasso替代Glide
            if (book.getImageUrl() != null && !book.getImageUrl().isEmpty()) {
                Picasso.get()
                        .load(book.getImageUrl())
                        .placeholder(R.drawable.default_book_cover)
                        .error(R.drawable.default_book_cover)
                        .into(coverImageView);
            } else {
                coverImageView.setImageResource(R.drawable.default_book_cover);
            }

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onBookClick(book);
                }
            });
        }
    }

    public interface OnBookClickListener {
        void onBookClick(BookInfo book);
    }
} 