package com.fanqie.novel.strategy;


import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.data.model.ChapterInfo;

import java.util.List;

public interface ChapterDownloadStrategy {

    /**
     * 下载指定章节内容
     *
     * @param chapter 章节信息对象
     * @param bookId 书籍ID（可选）
     * @param callback 下载结果回调
     */
    void downloadChapter(ChapterInfo chapter, String bookId, DownloadCallback callback);

    /**
     * 获取书籍信息（含章节列表等）
     *
     * @param bookId   书籍ID
     * @param callback 结果回调
     */
    void getBookInfo(String bookId, BookInfoCallback callback);

    /**
     * 是否支持批量下载
     */
    boolean supportsBatchDownload();

    /**
     * 批量下载章节内容
     *
     * @param chapters 章节列表
     * @param callback 下载结果回调
     */
    void downloadChapters(List<ChapterInfo> chapters, DownloadCallback callback);

    interface BookInfoCallback {
        void onSuccess(BookInfo bookInfo);

        void onFailure(Throwable error);
    }
} 