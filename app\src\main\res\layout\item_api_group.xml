<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="12dp"
    android:layout_marginBottom="4dp"
    android:layout_marginHorizontal="0dp"
    android:elevation="4dp"
    app:cardCornerRadius="8dp"
    app:cardBackgroundColor="@color/group_card_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingStart="20dp"
        android:paddingEnd="12dp"
        android:paddingTop="14dp"
        android:paddingBottom="14dp">

        <TextView
            android:id="@+id/tv_group_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="API分组"
            android:textStyle="bold"
            android:textSize="16sp"
            android:textColor="#222222"/>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView> 