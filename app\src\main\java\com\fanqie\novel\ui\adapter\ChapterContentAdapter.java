package com.fanqie.novel.ui.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.fanqie.novel.R;
import com.fanqie.novel.util.HtmlUtils;
import com.fanqie.novel.util.LogUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 章节内容适配器
 * 用于在RecyclerView中显示章节的文本内容
 */
public class ChapterContentAdapter extends RecyclerView.Adapter<ChapterContentAdapter.ContentViewHolder> {
    private static final String TAG = "ChapterContentAdapter";
    
    // 章节内容段落
    private List<String> paragraphs = new ArrayList<>();
    // 章节标题
    private String chapterTitle;
    // 字体大小
    private int textSize = 18;
    
    // 分页相关
    private boolean isPagingEnabled = false;
    private List<List<String>> pages = new ArrayList<>();
    private int currentPageIndex = 0;
    private static final int CHARS_PER_PAGE = 3000; // 每页字符数
    
    public ChapterContentAdapter() {
    }
    
    /**
     * 设置章节内容，自动按照换行符分割为段落
     * 并清理HTML标签
     * @param content 章节内容文本
     */
    public void setContent(String content) {
        if (TextUtils.isEmpty(content)) {
            paragraphs.clear();
            pages.clear();
        } else {
            try {
                // 清理HTML标签
                String cleanContent = HtmlUtils.cleanHtml(content);
                
                // 按换行符分割文本
                String[] lines = cleanContent.split("\n");
                List<String> tempList = new ArrayList<>();
                
                // 提取可能的章节标题
                if (lines.length > 0) {
                    String firstLine = lines[0].trim();
                    // 匹配章节标题格式：第X章 XXXX
                    if (firstLine.matches("^第[0-9一二三四五六七八九十百千万]+[章节卷集].*")) {
                        chapterTitle = firstLine;
                        // 如果第一行是章节标题，跳过
                        tempList.addAll(Arrays.asList(lines).subList(1, lines.length));
                    } else {
                        chapterTitle = "";
                        tempList.addAll(Arrays.asList(lines));
                    }
                }
                
                // 处理段落，过滤空行和合并短段落
                paragraphs = processContent(tempList);
                
                // 如果启用分页，则进行章节分页处理
                if (isPagingEnabled) {
                    paginateContent();
                    displayCurrentPage();
                } else {
                    // 直接通知数据变化
                    notifyDataSetChanged();
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "解析章节内容失败: " + e.getMessage());
                paragraphs.clear();
                pages.clear();
                // 添加一个错误提示
                paragraphs.add("加载章节内容失败，请重试");
                notifyDataSetChanged();
            }
        }
    }
    
    /**
     * 处理内容段落
     * 合并短句、去除空行和冗余内容
     */
    private List<String> processContent(List<String> rawLines) {
        List<String> result = new ArrayList<>();
        StringBuilder buffer = new StringBuilder();
        
        for (String line : rawLines) {
            String trimmed = line.trim();
            
            // 跳过空行或无意义内容
            if (TextUtils.isEmpty(trimmed) || 
                trimmed.equals("本章完") ||
                trimmed.contains("PS:") ||
                trimmed.contains("章节目录") ||
                trimmed.contains("手机阅读") ||
                trimmed.contains("提示:") ||
                trimmed.contains("温馨提示") ||
                trimmed.contains("下载APP")) {
                continue;
            }
            
            // 移除行首缩进空格
            if (trimmed.startsWith("    ")) {
                trimmed = trimmed.substring(4);
            } else if (trimmed.startsWith("　　")) {
                trimmed = trimmed.substring(2);
            }
            
            // 如果是短句且不是对话，可能需要合并
            if (trimmed.length() < 20 && !trimmed.contains("") && !trimmed.contains("：")){
                if (buffer.length() > 0) {
                    buffer.append("，").append(trimmed);
                } else {
                    buffer.append(trimmed);
                }
            } else {
                // 先处理之前的缓冲区
                if (buffer.length() > 0) {
                    result.add(buffer.toString());
                    buffer.setLength(0);
                }

                // 添加当前行
                result.add(trimmed);
            }
        }
        
        // 确保缓冲区最后内容也被添加
        if (buffer.length() > 0) {
            result.add(buffer.toString());
        }
        
        return result;
    }
    
    /**
     * 分页处理
     * 将章节内容按照每页字符数分成多个页面
     */
    private void paginateContent() {
        pages.clear();
        
        if (paragraphs.isEmpty()) {
            return;
        }
        
        List<String> currentPage = new ArrayList<>();
        int charCount = 0;
        
        for (String paragraph : paragraphs) {
            // 如果当前段落加上已有内容超过页面大小，创建新页面
            if (charCount + paragraph.length() > CHARS_PER_PAGE && charCount > 0) {
                pages.add(new ArrayList<>(currentPage));
                currentPage.clear();
                charCount = 0;
            }
            
            // 如果单个段落超过页面大小，需要拆分段落
            if (paragraph.length() > CHARS_PER_PAGE) {
                int start = 0;
                while (start < paragraph.length()) {
                    int end = Math.min(start + CHARS_PER_PAGE, paragraph.length());
                    String part = paragraph.substring(start, end);
                    
                    // 如果当前页已有内容，先保存当前页
                    if (!currentPage.isEmpty()) {
                        pages.add(new ArrayList<>(currentPage));
                        currentPage.clear();
                    }
                    
                    // 将拆分的段落部分作为一个独立页面
                    List<String> partPage = new ArrayList<>();
                    partPage.add(part);
                    pages.add(partPage);
                    
                    start = end;
                }
            } else {
                // 普通段落，添加到当前页
                currentPage.add(paragraph);
                charCount += paragraph.length();
            }
        }
        
        // 添加最后一页
        if (!currentPage.isEmpty()) {
            pages.add(currentPage);
        }
        
        // 重置当前页面索引
        currentPageIndex = 0;
        
        LogUtil.d(TAG, "章节分页完成，共" + pages.size() + "页");
    }
    
    /**
     * 显示当前页内容
     */
    private void displayCurrentPage() {
        if (pages.isEmpty() || currentPageIndex >= pages.size()) {
            paragraphs.clear();
        } else {
            paragraphs = new ArrayList<>(pages.get(currentPageIndex));
        }
        notifyDataSetChanged();
    }
    
    /**
     * 设置是否启用分页
     * @param enabled 是否启用
     */
    public void setPagingEnabled(boolean enabled) {
        if (this.isPagingEnabled != enabled) {
            this.isPagingEnabled = enabled;
            if (enabled && !paragraphs.isEmpty()) {
                paginateContent();
                displayCurrentPage();
            } else if (!enabled && !pages.isEmpty()) {
                // 合并所有页面的段落
                paragraphs.clear();
                for (List<String> page : pages) {
                    paragraphs.addAll(page);
                }
                notifyDataSetChanged();
            }
        }
    }
    
    /**
     * 获取当前页索引
     */
    public int getCurrentPageIndex() {
        return currentPageIndex;
    }
    
    /**
     * 获取总页数
     */
    public int getTotalPages() {
        return pages.size();
    }
    
    /**
     * 获取页面导航信息
     */
    public String getPageInfo() {
        if (!isPagingEnabled || pages.isEmpty()) {
            return "";
        }
        return (currentPageIndex + 1) + "/" + pages.size();
    }
    
    /**
     * 跳转到下一页
     * @return 是否成功跳转
     */
    public boolean nextPage() {
        if (!isPagingEnabled || currentPageIndex >= pages.size() - 1) {
            return false;
        }
        
        currentPageIndex++;
        displayCurrentPage();
        return true;
    }
    
    /**
     * 跳转到上一页
     * @return 是否成功跳转
     */
    public boolean previousPage() {
        if (!isPagingEnabled || currentPageIndex <= 0) {
            return false;
        }
        
        currentPageIndex--;
        displayCurrentPage();
        return true;
    }
    
    /**
     * 跳转到指定页
     * @param pageIndex 页面索引
     * @return 是否成功跳转
     */
    public boolean goToPage(int pageIndex) {
        if (!isPagingEnabled || pageIndex < 0 || pageIndex >= pages.size()) {
            return false;
        }
        
        if (pageIndex != currentPageIndex) {
            currentPageIndex = pageIndex;
            displayCurrentPage();
        }
        return true;
    }
    
    /**
     * 设置字体大小
     * @param textSize 字体大小(sp)
     */
    public void setTextSize(int textSize) {
        this.textSize = textSize;
        notifyDataSetChanged();
    }
    
    /**
     * 获取章节标题
     */
    public String getChapterTitle() {
        return chapterTitle;
    }
    
    @NonNull
    @Override
    public ContentViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_paragraph, parent, false);
        return new ContentViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ContentViewHolder holder, int position) {
        holder.tvParagraph.setText(paragraphs.get(position));
        holder.tvParagraph.setTextSize(textSize);
    }
    
    @Override
    public int getItemCount() {
        return paragraphs.size();
    }
    
    /**
     * 获取阅读进度百分比
     * @param position 当前项位置
     * @return 进度百分比文本
     */
    public String getReadingProgress(int position) {
        if (paragraphs.isEmpty()) return "0%";
        
        if (isPagingEnabled) {
            // 分页模式下返回页面进度
            int progress = (int) (((currentPageIndex) * 100.0f) / Math.max(1, pages.size() - 1));
            return progress + "%";
        } else {
            // 非分页模式返回段落进度
            int progress = (int) ((position * 100.0f) / paragraphs.size());
            return progress + "%";
        }
    }
    
    static class ContentViewHolder extends RecyclerView.ViewHolder {
        TextView tvParagraph;
        
        public ContentViewHolder(@NonNull View itemView) {
            super(itemView);
            tvParagraph = itemView.findViewById(R.id.tv_paragraph);
        }
    }
}