package com.fanqie.novel.util;

import android.content.Context;
import com.fanqie.novel.constants.DownloadStrategyType;
import com.fanqie.novel.data.db.mapper.DownloadConfigMapper;
import com.fanqie.novel.data.model.ApiEndpoint;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.domain.BatchConfig;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DownloadConfigManager {
    private static volatile DownloadConfigManager instance;

    private DownloadStrategyType currentStrategy;
    private List<ApiEndpoint> apiEndpoints = new ArrayList<>();
    private boolean apiFetched = false;
    private final Map<String, BookInfo> bookInfoCache = new ConcurrentHashMap<>();
    private final Context context;
    private BatchConfig batchConfig = new BatchConfig();

    private DownloadConfigManager(Context context) {
        this.context = context.getApplicationContext();
        // 查询当前激活配置
        DownloadConfigMapper.ConfigRecord current = DownloadConfigMapper.getInstance(this.context).queryCurrentConfig();
        if (current != null) {
            try {
                currentStrategy = DownloadStrategyType.valueOf(current.strategy);
            } catch (Exception e) {
                currentStrategy = DownloadStrategyType.LEGACY;
            }
        } else {
            try {
                // 首次初始化，插入默认LEGACY配置并激活
                JSONObject legacy = new JSONObject()
                    .put("installId", "4427064614339001")
                    .put("serverDeviceId", "4427064614334905")
                    .put("aid", "1967")
                    .put("updateVersionCode", "62532");
                DownloadConfigMapper.getInstance(this.context).insertOrUpdateConfig("LEGACY", legacy.toString(), true);
                currentStrategy = DownloadStrategyType.LEGACY;
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public DownloadStrategyType getCurrentStrategy() {
        return currentStrategy;
    }

    public void setCurrentStrategy(DownloadStrategyType strategy) {
        this.currentStrategy = strategy;
        DownloadConfigMapper.getInstance(context).setCurrentStrategy(strategy.name());
    }

    public JSONObject getConfig(DownloadStrategyType strategy) {
        DownloadConfigMapper.ConfigRecord rec = DownloadConfigMapper.getInstance(context).queryConfigByStrategy(strategy.name());
        if (rec != null && rec.configJson != null) {
            try { return new JSONObject(rec.configJson); } catch (Exception e) { return new JSONObject(); }
        }
        return new JSONObject();
    }

    public void setConfig(DownloadStrategyType strategy, JSONObject config) {
        boolean isCurrent = (strategy == currentStrategy);
        DownloadConfigMapper.getInstance(context).insertOrUpdateConfig(strategy.name(), config.toString(), isCurrent);
    }

    public JSONObject getPythonLikeConfig() { return getConfig(DownloadStrategyType.PYTHON_LIKE); }

    public String getName() {
        JSONObject py = getPythonLikeConfig();
        return py.optString("name", "qyuing");
    }
    public void setName(String name) {
        JSONObject py = getPythonLikeConfig();
        try {
            py.put("name", name);
            setConfig(DownloadStrategyType.PYTHON_LIKE, py);
        } catch (Exception ignored) {}
    }
    public int getBatchSize() {
        JSONObject py = getPythonLikeConfig();
        return py.optInt("max_batch_size", 10);
    }
    public void setBatchSize(int batchSize) {
        JSONObject py = getPythonLikeConfig();
        try {
            py.put("max_batch_size", batchSize);
            setConfig(DownloadStrategyType.PYTHON_LIKE, py);
        } catch (Exception ignored) {}
    }
    public String getServerUrl() {
        JSONObject py = getPythonLikeConfig();
        return py.optString("server_url", "");
    }
    public void setServerUrl(String url) {
        JSONObject py = getPythonLikeConfig();
        try {
            py.put("server_url", url);
            setConfig(DownloadStrategyType.PYTHON_LIKE, py);
        } catch (Exception ignored) {}
    }
    public String getAuthToken() {
        JSONObject py = getPythonLikeConfig();
        return py.optString("auth_token", "");
    }
    public void setAuthToken(String token) {
        JSONObject py = getPythonLikeConfig();
        try {
            py.put("auth_token", token);
            setConfig(DownloadStrategyType.PYTHON_LIKE, py);
        } catch (Exception ignored) {}
    }

    public static DownloadConfigManager getInstance(Context context) {
        if (instance == null) {
            synchronized (DownloadConfigManager.class) {
                if (instance == null) {
                    instance = new DownloadConfigManager(context);
                }
            }
        }
        return instance;
    }

    public List<ApiEndpoint> getApiEndpoints() {
        return apiEndpoints;
    }

    public void setApiEndpoints(List<ApiEndpoint> apiEndpoints) {
        this.apiEndpoints = apiEndpoints;
        this.apiFetched = true;
    }

    public boolean isApiFetched() {
        return apiFetched;
    }

    public void cacheBookInfo(BookInfo bookInfo) {
        if (bookInfo != null && bookInfo.getBookId() != null) {
            bookInfoCache.put(bookInfo.getBookId(), bookInfo);
        }
    }

    public BookInfo getBookInfoById(String bookId) {
        return bookInfoCache.get(bookId);
    }

    public BatchConfig getBatchConfig() {
        return batchConfig;
    }

    public void setBatchConfig(BatchConfig batchConfig) {
        this.batchConfig = batchConfig;
    }

}