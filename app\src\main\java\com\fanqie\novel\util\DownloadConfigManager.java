package com.fanqie.novel.util;

import android.content.Context;
import com.fanqie.novel.constants.DownloadStrategyType;
import com.fanqie.novel.data.db.mapper.DownloadConfigMapper;
import com.fanqie.novel.data.model.ApiEndpoint;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.domain.BatchConfig;
import com.fanqie.novel.util.LogUtil;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class DownloadConfigManager {
    private static final String TAG = "DownloadConfigManager";
    private static volatile DownloadConfigManager instance;

    private DownloadStrategyType currentStrategy;
    private List<ApiEndpoint> apiEndpoints = new ArrayList<>();
    private boolean apiFetched = false;
    private final Map<String, BookInfo> bookInfoCache = new ConcurrentHashMap<>();
    private final Context context;
    private BatchConfig batchConfig = new BatchConfig();

    // JSON配置缓存
    private JsonConfigParser.ParseResult cachedJsonConfig = null;
    private boolean jsonConfigParsed = false;

    // 配置变更监听器
    private final List<ConfigChangeListener> configChangeListeners = new CopyOnWriteArrayList<>();

    /**
     * 配置变更监听器接口
     */
    public interface ConfigChangeListener {
        void onConfigChanged(DownloadStrategyType strategy, String configType);
    }

    private DownloadConfigManager(Context context) {
        this.context = context.getApplicationContext();
        // 查询当前激活配置
        DownloadConfigMapper.ConfigRecord current = DownloadConfigMapper.getInstance(this.context).queryCurrentConfig();
        if (current != null) {
            try {
                currentStrategy = DownloadStrategyType.valueOf(current.strategy);
            } catch (Exception e) {
                currentStrategy = DownloadStrategyType.LEGACY;
            }
        } else {
            try {
                // 首次初始化，插入默认LEGACY配置并激活
                JSONObject legacy = new JSONObject()
                        .put("installId", "4427064614339001")
                        .put("serverDeviceId", "4427064614334905")
                        .put("aid", "1967")
                        .put("updateVersionCode", "62532");
                DownloadConfigMapper.getInstance(this.context).insertOrUpdateConfig("LEGACY", legacy.toString(), true);
                currentStrategy = DownloadStrategyType.LEGACY;
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public DownloadStrategyType getCurrentStrategy() {
        return currentStrategy;
    }

    public void setCurrentStrategy(DownloadStrategyType strategy) {
        this.currentStrategy = strategy;
        DownloadConfigMapper.getInstance(context).setCurrentStrategy(strategy.name());
        // 通知配置变更
        notifyConfigChanged(strategy, "strategy");
    }

    public JSONObject getConfig(DownloadStrategyType strategy) {
        DownloadConfigMapper.ConfigRecord rec = DownloadConfigMapper.getInstance(context)
                .queryConfigByStrategy(strategy.name());
        if (rec != null && rec.configJson != null) {
            try {
                return new JSONObject(rec.configJson);
            } catch (Exception e) {
                return new JSONObject();
            }
        }
        return new JSONObject();
    }

    public void setConfig(DownloadStrategyType strategy, JSONObject config) {
        boolean isCurrent = (strategy == currentStrategy);
        DownloadConfigMapper.getInstance(context).insertOrUpdateConfig(strategy.name(), config.toString(), isCurrent);
        // 通知配置变更
        notifyConfigChanged(strategy, "config");
    }

    public JSONObject getPythonLikeConfig() {
        return getConfig(DownloadStrategyType.PYTHON_LIKE);
    }

    public String getName() {
        JSONObject py = getPythonLikeConfig();
        return py.optString("name", "qyuing");
    }

    public void setName(String name) {
        JSONObject py = getPythonLikeConfig();
        try {
            py.put("name", name);
            setConfig(DownloadStrategyType.PYTHON_LIKE, py);
        } catch (Exception ignored) {
        }
    }

    public int getBatchSize() {
        JSONObject py = getPythonLikeConfig();
        return py.optInt("max_batch_size", 10);
    }

    public void setBatchSize(int batchSize) {
        JSONObject py = getPythonLikeConfig();
        try {
            py.put("max_batch_size", batchSize);
            setConfig(DownloadStrategyType.PYTHON_LIKE, py);
        } catch (Exception ignored) {
        }
    }

    public String getServerUrl() {
        JSONObject py = getPythonLikeConfig();
        return py.optString("server_url", "");
    }

    public void setServerUrl(String url) {
        JSONObject py = getPythonLikeConfig();
        try {
            py.put("server_url", url);
            setConfig(DownloadStrategyType.PYTHON_LIKE, py);
        } catch (Exception ignored) {
        }
    }

    public String getAuthToken() {
        JSONObject py = getPythonLikeConfig();
        return py.optString("auth_token", "");
    }

    public void setAuthToken(String token) {
        JSONObject py = getPythonLikeConfig();
        try {
            py.put("auth_token", token);
            setConfig(DownloadStrategyType.PYTHON_LIKE, py);
        } catch (Exception ignored) {
        }
    }

    public static DownloadConfigManager getInstance(Context context) {
        if (instance == null) {
            synchronized (DownloadConfigManager.class) {
                if (instance == null) {
                    instance = new DownloadConfigManager(context);
                }
            }
        }
        return instance;
    }

    public List<ApiEndpoint> getApiEndpoints() {
        return apiEndpoints;
    }

    public void setApiEndpoints(List<ApiEndpoint> apiEndpoints) {
        this.apiEndpoints = apiEndpoints;
        this.apiFetched = true;
        // 通知配置变更
        notifyConfigChanged(DownloadStrategyType.PYTHON_LIKE, "api_endpoints");
    }

    public boolean isApiFetched() {
        return apiFetched;
    }

    public void cacheBookInfo(BookInfo bookInfo) {
        if (bookInfo != null && bookInfo.getBookId() != null) {
            bookInfoCache.put(bookInfo.getBookId(), bookInfo);
        }
    }

    public BookInfo getBookInfoById(String bookId) {
        return bookInfoCache.get(bookId);
    }

    public BatchConfig getBatchConfig() {
        return batchConfig;
    }

    public void setBatchConfig(BatchConfig batchConfig) {
        this.batchConfig = batchConfig;
    }

    /**
     * 获取JSON配置内容
     */
    public String getJsonConfig() {
        JSONObject config = getPythonLikeConfig();
        return config.optString("json_config", "");
    }

    /**
     * 设置JSON配置内容
     */
    public void setJsonConfig(String jsonConfig) {
        JSONObject config = getPythonLikeConfig();
        try {
            config.put("json_config", jsonConfig);
            setConfig(DownloadStrategyType.PYTHON_LIKE, config);
            // 清除缓存，强制重新解析
            clearJsonConfigCache();
            LogUtil.i(TAG, "JSON配置已保存");
            // 通知配置变更
            notifyConfigChanged(DownloadStrategyType.PYTHON_LIKE, "json_config");
        } catch (JSONException e) {
            LogUtil.e(TAG, "保存JSON配置失败", e);
        }
    }

    /**
     * 解析JSON配置并缓存结果
     */
    public JsonConfigParser.ParseResult parseJsonConfig() {
        if (jsonConfigParsed && cachedJsonConfig != null) {
            return cachedJsonConfig;
        }

        String jsonConfig = getJsonConfig();
        if (jsonConfig.isEmpty()) {
            LogUtil.d(TAG, "JSON配置为空");
            cachedJsonConfig = new JsonConfigParser.ParseResult();
            cachedJsonConfig.success = false;
            cachedJsonConfig.errorMessage = "JSON配置为空";
        } else {
            LogUtil.d(TAG, "开始解析JSON配置");
            cachedJsonConfig = JsonConfigParser.parseJsonConfig(jsonConfig);
            if (cachedJsonConfig.success) {
                LogUtil.i(TAG, "JSON配置解析成功，包含 " + cachedJsonConfig.apiEndpoints.size() + " 个API端点");
            } else {
                LogUtil.e(TAG, "JSON配置解析失败: " + cachedJsonConfig.errorMessage);
            }
        }

        jsonConfigParsed = true;
        return cachedJsonConfig;
    }

    /**
     * 清除JSON配置缓存
     */
    public void clearJsonConfigCache() {
        cachedJsonConfig = null;
        jsonConfigParsed = false;
        LogUtil.d(TAG, "JSON配置缓存已清除");
    }

    /**
     * 获取API端点配置（双重获取机制）
     * 优先使用URL获取的配置，如果失败则使用JSON配置
     */
    public List<ApiEndpoint> getApiEndpointsWithFallback() {
        // 优先使用URL获取的配置
        if (apiFetched && !apiEndpoints.isEmpty()) {
            LogUtil.d(TAG, "使用URL获取的API端点配置，数量: " + apiEndpoints.size());
            return new ArrayList<>(apiEndpoints);
        }

        // 如果URL获取失败，尝试使用JSON配置
        LogUtil.i(TAG, "URL配置不可用，尝试使用JSON配置");
        JsonConfigParser.ParseResult jsonResult = parseJsonConfig();
        if (jsonResult.success && !jsonResult.apiEndpoints.isEmpty()) {
            LogUtil.i(TAG, "使用JSON配置的API端点，数量: " + jsonResult.apiEndpoints.size());
            return new ArrayList<>(jsonResult.apiEndpoints);
        }

        LogUtil.w(TAG, "URL和JSON配置都不可用，返回空列表");
        return new ArrayList<>();
    }

    /**
     * 获取批量配置（双重获取机制）
     */
    public BatchConfig getBatchConfigWithFallback() {
        // 优先使用URL获取的配置
        if (batchConfig != null && batchConfig.isEnabled()) {
            LogUtil.d(TAG, "使用URL获取的批量配置");
            return batchConfig;
        }

        // 如果URL获取失败，尝试使用JSON配置
        LogUtil.i(TAG, "URL批量配置不可用，尝试使用JSON配置");
        JsonConfigParser.ParseResult jsonResult = parseJsonConfig();
        if (jsonResult.success && jsonResult.batchConfig != null) {
            LogUtil.i(TAG, "使用JSON配置的批量配置");
            return jsonResult.batchConfig;
        }

        LogUtil.w(TAG, "URL和JSON批量配置都不可用，返回默认配置");
        return batchConfig != null ? batchConfig : new BatchConfig();
    }

    /**
     * 检查是否有可用的配置（URL或JSON）
     */
    public boolean hasValidConfig() {
        // 检查URL配置
        if (apiFetched && !apiEndpoints.isEmpty()) {
            return true;
        }

        // 检查JSON配置
        JsonConfigParser.ParseResult jsonResult = parseJsonConfig();
        return jsonResult.success && !jsonResult.apiEndpoints.isEmpty();
    }

    /**
     * 获取配置来源信息
     */
    public String getConfigSource() {
        if (apiFetched && !apiEndpoints.isEmpty()) {
            return "URL配置";
        }

        JsonConfigParser.ParseResult jsonResult = parseJsonConfig();
        if (jsonResult.success && !jsonResult.apiEndpoints.isEmpty()) {
            return "JSON配置";
        }

        return "无可用配置";
    }

    /**
     * 添加配置变更监听器
     */
    public void addConfigChangeListener(ConfigChangeListener listener) {
        if (listener != null && !configChangeListeners.contains(listener)) {
            configChangeListeners.add(listener);
            LogUtil.d(TAG, "添加配置变更监听器，当前监听器数量: " + configChangeListeners.size());
        }
    }

    /**
     * 移除配置变更监听器
     */
    public void removeConfigChangeListener(ConfigChangeListener listener) {
        if (listener != null) {
            configChangeListeners.remove(listener);
            LogUtil.d(TAG, "移除配置变更监听器，当前监听器数量: " + configChangeListeners.size());
        }
    }

    /**
     * 通知配置变更
     */
    private void notifyConfigChanged(DownloadStrategyType strategy, String configType) {
        LogUtil.i(TAG, "配置变更通知: " + strategy + " - " + configType + ", 监听器数量: " + configChangeListeners.size());
        for (ConfigChangeListener listener : configChangeListeners) {
            try {
                listener.onConfigChanged(strategy, configType);
            } catch (Exception e) {
                LogUtil.e(TAG, "配置变更监听器执行异常", e);
            }
        }
    }

    /**
     * 强制刷新所有缓存配置
     */
    public void refreshAllConfigs() {
        LogUtil.i(TAG, "强制刷新所有配置缓存");
        // 清除API端点缓存
        apiFetched = false;
        apiEndpoints.clear();

        // 清除JSON配置缓存
        clearJsonConfigCache();

        // 清除批量配置缓存
        batchConfig = new BatchConfig();

        // 通知配置刷新
        notifyConfigChanged(currentStrategy, "refresh_all");
    }

}