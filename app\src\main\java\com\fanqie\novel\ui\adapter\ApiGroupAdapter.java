package com.fanqie.novel.ui.adapter;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.fanqie.novel.R;

import java.util.List;

public class ApiGroupAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    public static final int TYPE_GROUP = 0;
    public static final int TYPE_ENTRY = 1;

    private final List<Object> dataList;
    private final Context context;

    public ApiGroupAdapter(Context context, List<Object> dataList) {
        this.context = context;
        this.dataList = dataList;
    }

    @Override
    public int getItemViewType(int position) {
        Object obj = dataList.get(position);
        if (obj instanceof ApiGroup) return TYPE_GROUP;
        return TYPE_ENTRY;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_GROUP) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_api_group, parent, false);
            return new GroupViewHolder(view);
        } else {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_api_entry, parent, false);
            return new EntryViewHolder(view);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof GroupViewHolder) {
            ApiGroup group = (ApiGroup) dataList.get(position);
            ((GroupViewHolder) holder).bind(group);
        } else if (holder instanceof EntryViewHolder) {
            ApiEntry entry = (ApiEntry) dataList.get(position);
            ((EntryViewHolder) holder).bind(entry);
        }
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public static class ApiGroup {
        public String groupName;
        public List<ApiEntry> entryList;
        public ApiGroup(String groupName, List<ApiEntry> entryList) {
            this.groupName = groupName;
            this.entryList = entryList;
        }
    }

    public static class ApiEntry {
        public String name;
        public String url;
        public ApiEntry(String name, String url) {
            this.name = name;
            this.url = url;
        }
    }

    class GroupViewHolder extends RecyclerView.ViewHolder {
        TextView tvGroupTitle;
        public GroupViewHolder(@NonNull View itemView) {
            super(itemView);
            tvGroupTitle = itemView.findViewById(R.id.tv_group_title);
        }
        void bind(ApiGroup group) {
            tvGroupTitle.setText(group.groupName);
        }
    }

    class EntryViewHolder extends RecyclerView.ViewHolder {
        TextView tvApiName, tvApiUrl;
        ImageButton btnCopy;
        public EntryViewHolder(@NonNull View itemView) {
            super(itemView);
            tvApiName = itemView.findViewById(R.id.tv_api_name);
            tvApiUrl = itemView.findViewById(R.id.tv_api_url);
            btnCopy = itemView.findViewById(R.id.btn_copy);
        }
        void bind(ApiEntry entry) {
            if (entry.name == null || entry.name.trim().isEmpty()) {
                tvApiName.setVisibility(View.GONE);
            } else {
                tvApiName.setVisibility(View.VISIBLE);
                tvApiName.setText(entry.name);
            }
            tvApiUrl.setText(entry.url);
            btnCopy.setOnClickListener(v -> {
                ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData clip = ClipData.newPlainText("API地址", entry.url);
                clipboard.setPrimaryClip(clip);
                Toast.makeText(context, "已复制API地址", Toast.LENGTH_SHORT).show();
            });
        }
    }
} 