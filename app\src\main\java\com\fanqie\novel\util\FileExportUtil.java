package com.fanqie.novel.util;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import androidx.activity.result.ActivityResult;
import cn.hutool.core.util.StrUtil;
import com.fanqie.novel.data.db.mapper.BookMapper;
import com.fanqie.novel.data.db.mapper.ChapterMapper;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.data.model.ChapterInfo;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

public class FileExportUtil {
    private static final String TAG = "FileExportUtil";
    private static final int BUFFER_SIZE = 8192;
    private static final ExecutorService executorService =
            Executors.newSingleThreadExecutor();

    // 存储当前正在处理的临时文件路径
    private static String currentTempFilePath;

    /**
     * 异步准备文件内容并创建临时文件
     *
     * @param context       上下文对象,用于获取缓存目录
     * @param bookId        书籍ID
     * @param filename      文件名
     * @param chapterMapper 章节数据访问对象
     * @return 返回包含临时文件的CompletableFuture对象
     */
    public static CompletableFuture<File> prepareTempFileAsync(Context context,
                                                               String bookId, String filename, ChapterMapper chapterMapper) {
        LogUtil.setMdcContext(bookId); // 设置MDC上下文
        LogUtil.i(TAG, String.format("开始准备文件: bookId=%s, filename=%s", bookId, filename));

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 在这里获取 MDC 上下文
                LogUtil.setMdcContext(bookId);
                LogUtil.d(TAG, "开始获取书籍内容...");
                // 1. 准备文件内容
                String fileContent = prepareContent(bookId, chapterMapper);
                if (StrUtil.isEmpty(fileContent)) {
                    LogUtil.e(TAG, "生成的内容为空: bookId=" + bookId);
                    return null;
                }
                LogUtil.i(TAG, String.format(Locale.getDefault(), "内容准备完成，长度: %d 字符", fileContent.length()));

                // 2. 创建临时文件
                LogUtil.d(TAG, "开始创建临时文件...");
                File tempFile = createTempFile(context, filename);
                LogUtil.i(TAG, "临时文件创建成功: " + tempFile.getAbsolutePath());

                // 3. 写入内容到临时文件
                LogUtil.d(TAG, "开始写入文件内容...");
                boolean writeSuccess = writeContentToFile(tempFile, fileContent);
                if (!writeSuccess) {
                    LogUtil.e(TAG, "写入文件失败");
                    return null;
                }
                LogUtil.i(TAG, String.format(Locale.getDefault(), "文件写入成功，大小: %d 字节", tempFile.length()));

                return tempFile;
            } catch (Exception e) {
                LogUtil.e(TAG, "准备文件过程中发生错误", e);
                return null;
            } finally {
                LogUtil.clearMdcContext(); // 清除MDC上下文
            }
        }, executorService);
    }

    /**
     * 处理文件保存结果
     * 将临时文件复制到用户选择的目标位置
     *
     * @param result          Activity返回的结果,包含目标URI
     * @param contentResolver 内容解析器,用于访问目标URI
     */
    public static void handleSaveResult(ActivityResult result, ContentResolver contentResolver) {
        LogUtil.i(TAG, "开始处理文件保存结果");

        if (result.getResultCode() != Activity.RESULT_OK || result.getData() == null) {
            LogUtil.w(TAG, "用户取消保存或未收到数据");
            return;
        }

        Uri uri = result.getData().getData();
        if (uri == null) {
            LogUtil.e(TAG, "未收到目标URI");
            return;
        }
        LogUtil.d(TAG, "目标保存URI: " + uri);

        CompletableFuture.runAsync(() -> {
            try {
                File tempFile = new File(currentTempFilePath);
                if (!tempFile.exists()) {
                    LogUtil.e(TAG, "临时文件不存在: " + currentTempFilePath);
                    return;
                }
                copyFileToUri(tempFile, uri, contentResolver);
            } finally {
                LogUtil.d(TAG, "开始清理临时文件");
                cleanupTempFile(new File(currentTempFilePath));
            }
        }, executorService);
    }

    /**
     * 将源文件复制到目标URI
     * 使用缓冲流提高复制效率
     *
     * @param sourceFile      源文件
     * @param destUri         目标URI
     * @param contentResolver 内容解析器
     */
    private static void copyFileToUri(File sourceFile, Uri destUri,
                                      ContentResolver contentResolver) {
        LogUtil.i(TAG, "开始复制文件到目标URI");
        long startTime = System.currentTimeMillis();
        long totalBytes = 0;

        try (InputStream in = new BufferedInputStream(new FileInputStream(sourceFile));
             OutputStream out = new BufferedOutputStream(
                     contentResolver.openOutputStream(destUri))) {

            byte[] buffer = new byte[BUFFER_SIZE];
            int length;
            while ((length = in.read(buffer)) > 0) {
                out.write(buffer, 0, length);
                totalBytes += length;
            }
            out.flush();

            long endTime = System.currentTimeMillis();
            LogUtil.i(TAG, String.format(Locale.getDefault(), "文件复制完成: 总大小=%d bytes, 耗时=%d ms",
                    totalBytes, (endTime - startTime)));
        } catch (IOException e) {
            LogUtil.e(TAG, "复制文件时发生错误", e);
        }
    }

    /**
     * 创建文件选择器Intent
     * 用于让用户选择文件保存位置
     *
     * @param filename 建议的文件名
     * @param tempFile 临时文件,用于记录路径
     * @return 配置好的文件选择器Intent
     */
    public static Intent createSaveIntent(String filename, File tempFile) {
        currentTempFilePath = tempFile.getAbsolutePath();
        LogUtil.d(TAG, "创建保存文件Intent: " + filename);

        Intent intent = new Intent(Intent.ACTION_CREATE_DOCUMENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("text/plain");
        intent.putExtra(Intent.EXTRA_TITLE, filename);

        return intent;
    }

    /**
     * 清理临时文件
     * 在文件处理完成后删除临时文件
     *
     * @param tempFile 要删除的临时文件
     */
    private static void cleanupTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            if (!tempFile.delete()) {
                LogUtil.w(TAG, "删除临时文件失败: " + tempFile.getAbsolutePath());
            } else {
                LogUtil.d(TAG, "临时文件删除成功");
            }
        }
    }

    /**
     * 准备文件内容
     * 获取书籍信息和章节内容,组装成完整的文本
     *
     * @param bookId        书籍ID
     * @param chapterMapper 章节数据访问对象
     * @return 组装好的文本内容
     */
    private static String prepareContent(String bookId, ChapterMapper chapterMapper) {
        LogUtil.i(TAG, "开始准备书籍内容: bookId=" + bookId);

        // 获取书籍信息
        BookMapper bookMapper = BookMapper.getInstance(chapterMapper.getContext());
        List<BookInfo> bookInfoList = bookMapper.getBookList(bookId);
        if (bookInfoList.isEmpty()) {
            LogUtil.e(TAG, "未找到书籍信息: bookId=" + bookId);
            return "";
        }

        BookInfo bookInfo = bookInfoList.get(0);
        LogUtil.d(TAG, String.format("获取到书籍信息: 书名=%s, 作者=%s",
                bookInfo.getBookName(), bookInfo.getAuthor()));

        StringBuilder contentBuilder = new StringBuilder();

        // 添加书籍信息
        contentBuilder.append(bookInfo.getBookName()).append("\n");
        contentBuilder.append("作者：").append(bookInfo.getAuthor()).append("\n");
        contentBuilder.append("标签：").append(bookInfo.getTags()).append("\n");
        contentBuilder.append("字数：").append(bookInfo.getCount()).append("\n");
        contentBuilder.append("\n简介：\n").append(StrUtil.replace(bookInfo.getAbstractContent(), " ", "\n")).append("\n");

        // 获取章节内容
        List<ChapterInfo> chapters = chapterMapper.getChaptersByBookId(bookId);
        LogUtil.i(TAG, String.format(Locale.getDefault(), "获取到 %d 个章节", chapters.size()));

        // 处理章节内容
        LogUtil.d(TAG, "开始处理章节内容...");
        String chaptersContent = chapters.stream()
                .sorted(Comparator.comparingInt(ChapterInfo::getSort))
                .map(chapter -> {
                    return chapter.getTitle() + "\n" +
                            (chapter.getContent() != null ? chapter.getContent() : "") + "\n\n";
                })
                .collect(Collectors.joining());

        contentBuilder.append(chaptersContent);
        String finalContent = contentBuilder.toString();
        LogUtil.i(TAG, String.format(Locale.getDefault(), "内容准备完成，总长度: %d 字符", finalContent.length()));

        return finalContent;
    }

    /**
     * 创建临时文件
     * 在应用缓存目录下创建临时文件
     *
     * @param context  上下文对象
     * @param filename 文件名
     * @return 创建的临时文件
     * @throws IOException 如果创建文件失败
     */
    private static File createTempFile(Context context, String filename) throws IOException {
        LogUtil.d(TAG, "开始创建临时文件: " + filename);

        File cacheDir = context.getCacheDir();
        File tempFile = new File(cacheDir, filename + "_temp");

        if (!cacheDir.exists() && !cacheDir.mkdirs()) {
            LogUtil.e(TAG, "创建缓存目录失败");
            throw new IOException("创建缓存目录失败");
        }

        if (tempFile.exists() && !tempFile.delete()) {
            LogUtil.e(TAG, "删除已存在的临时文件失败");
            throw new IOException("删除已存在的临时文件失败");
        }

        if (!tempFile.createNewFile()) {
            LogUtil.e(TAG, "创建新的临时文件失败");
            throw new IOException("创建新的临时文件失败");
        }

        LogUtil.i(TAG, "临时文件创建成功: " + tempFile.getAbsolutePath());
        return tempFile;
    }

    /**
     * 写入内容到文件
     * 使用UTF-8编码和缓冲写入器提高写入效率
     *
     * @param file    目标文件
     * @param content 要写入的内容
     * @return 写入是否成功
     */
    private static boolean writeContentToFile(File file, String content) {
        LogUtil.d(TAG, "开始写入文件内容");
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8),
                BUFFER_SIZE)) {
            writer.write(content);
            writer.flush();

            // 验证写入的文件大小
            long fileSize = file.length();

            if (fileSize < content.length()) {
                LogUtil.e(TAG, "警告: 写入的文件大小小于内容长度!");
                return false;
            }

            LogUtil.i(TAG, String.format(Locale.getDefault(), "文件写入成功，大小: %d 字节", fileSize));
            return true;
        } catch (IOException e) {
            LogUtil.e(TAG, "写入文件时发生错误", e);
            return false;
        }
    }
} 