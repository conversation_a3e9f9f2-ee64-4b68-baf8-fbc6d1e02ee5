package com.fanqie.novel.ui.activity;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.fanqie.novel.R;
import com.fanqie.novel.constants.CommonCache;
import com.fanqie.novel.data.db.mapper.BookMapper;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.data.model.ChapterInfo;
import com.fanqie.novel.service.NovelDownloadService;
import com.fanqie.novel.ui.adapter.ChapterAdapter;
import com.fanqie.novel.ui.fragment.BookshelfFragment;
import com.fanqie.novel.util.*;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.squareup.picasso.Picasso;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class BookDetailActivity extends AppCompatActivity {
    private static final String TAG = "BookDetailActivity";
    private static final int PERMISSION_REQUEST_CODE = 1;
    private static final int CHAPTERS_PER_PAGE = 30;

    // 创建一个线程池用于处理后台任务
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    // 创建一个主线程Handler用于更新UI
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    // UI组件
    private Toolbar toolbar;
    private TextView tvToolbarTitle;
    private ImageView ivBookCover;
    private TextView tvBookTitle;
    private TextView tvBookAuthor;
    private TextView tvWordCount;
    private TextView tvLastChapter;
    private TextView tvUpdateTime;
    private Button btnDownload;
    private Button btnExport;
    private ChipGroup chipGroupTags;
    private TextView tvBookIntro;
    private TextView tvExpandIntro;
    private TextView tvChapterCount;
    private TextView tvSortOrder;
    private TextView tvLoadMore;
    private FloatingActionButton fabBackToTop;
    private ProgressBar progressBar;
    private NestedScrollView nestedScrollView;

    // 下载进度UI组件
    private View downloadProgressContainer;
    private ProgressBar downloadProgressBar;
    private TextView downloadProgressText;

    // 数据
    private BookInfo bookInfo;
    private String bookId;
    private String bookName;
    private String imgUrl;
    private boolean isDescSort = false;
    private boolean showFullIntro = false;
    private int currentPage = 1;
    private boolean isLoadingMore = false;

    // 下载相关
    private BookMapper bookMapper;
    private BroadcastReceiver downloadProgressReceiver;

    // 适配器
    private ChapterAdapter chapterAdapter;

    // 文件保存处理
    private ActivityResultLauncher<Intent> saveFileLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_book_detail);

        // 初始化数据库
        bookMapper = BookMapper.getInstance(this);

        // 初始化UI组件
        initViews();

        // 设置工具栏
        setupToolbar();

        // 获取Intent传递的数据
        getIntentData();

        // 请求存储权限
        requestStoragePermission();

        // 初始化文件保存处理
        initSaveFileLauncher();

        // 注册下载进度广播接收器
        registerDownloadProgressReceiver();

        // 加载书籍数据
        loadBookData();

        // 设置监听器
        setupListeners();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        tvToolbarTitle = findViewById(R.id.toolbar_title);
        ivBookCover = findViewById(R.id.iv_book_cover);
        tvBookTitle = findViewById(R.id.tv_book_title);
        tvBookAuthor = findViewById(R.id.tv_book_author);
        tvWordCount = findViewById(R.id.tv_word_count);
        tvLastChapter = findViewById(R.id.tv_last_chapter);
        tvUpdateTime = findViewById(R.id.tv_update_time);
        btnDownload = findViewById(R.id.btn_download);
        btnExport = findViewById(R.id.btn_export);
        chipGroupTags = findViewById(R.id.chip_group_tags);
        tvBookIntro = findViewById(R.id.tv_book_intro);
        tvExpandIntro = findViewById(R.id.tv_expand_intro);
        tvChapterCount = findViewById(R.id.tv_chapter_count);
        tvSortOrder = findViewById(R.id.tv_sort_order);
        RecyclerView recyclerChapters = findViewById(R.id.recycler_chapters);
        tvLoadMore = findViewById(R.id.tv_load_more);
        fabBackToTop = findViewById(R.id.fab_back_to_top);
        progressBar = findViewById(R.id.progress_bar);
        nestedScrollView = findViewById(R.id.nested_scroll_view);

        // 初始化下载进度UI组件
        downloadProgressContainer = findViewById(R.id.download_progress_container);
        downloadProgressBar = findViewById(R.id.download_progress_bar);
        downloadProgressText = findViewById(R.id.download_progress_text);

        // 确保返回顶部按钮初始状态为隐藏
        fabBackToTop.setVisibility(View.GONE);

        // 设置RecyclerView
        recyclerChapters.setLayoutManager(new LinearLayoutManager(this));
        chapterAdapter = new ChapterAdapter();
        recyclerChapters.setAdapter(chapterAdapter);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowTitleEnabled(false);
        }
    }

    private void getIntentData() {
        Intent intent = getIntent();
        if (intent != null) {
            bookName = intent.getStringExtra("bookName");
            bookId = intent.getStringExtra("bookId");
            imgUrl = intent.getStringExtra("imgUrl");
            // 异步获取BookInfo，防止主线程网络请求
            progressBar.setVisibility(View.VISIBLE);
            executorService.execute(() -> {
                BookInfo info = DownUtil.getBookInfo(bookId);
                info.setBookId(bookId);
                info.setBookName(bookName);
                info.setImageUrl(imgUrl);
                mainHandler.post(() -> {
                    bookInfo = info;
                    tvToolbarTitle.setText(bookName);
                    progressBar.setVisibility(View.GONE);
                    // 立即展示基础信息
                    updateUIWithBookInfo();
                });
            });
        }
    }

    private void requestStoragePermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, PERMISSION_REQUEST_CODE);
        }
    }

    private void initSaveFileLauncher() {
        saveFileLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> BookDataExportUtil.handleSaveResult(result, getContentResolver(), this)
        );
    }

    private void loadBookData() {
        if (bookInfo != null) {
            // 立即展示基础信息
            updateUIWithBookInfo();
            progressBar.setVisibility(View.GONE);
            // 检查章节列表，若为空则异步加载
            if (bookInfo.getChapterList() == null || bookInfo.getChapterList().isEmpty()) {
                progressBar.setVisibility(View.VISIBLE);
                executorService.execute(() -> {
                    List<ChapterInfo> chapters = DownUtil.getChapterList(bookInfo.getBookId());
                    mainHandler.post(() -> {
                        progressBar.setVisibility(View.GONE);
                        if (!chapters.isEmpty()) {
                            bookInfo.setChapterList(chapters);
                            updateChapterListWithPage();
                            updateLoadMoreVisibility();
                        } else {
                            tvLoadMore.setVisibility(View.GONE);
                            tvChapterCount.setText("无章节");
                        }
                    });
                });
            }
            return;
        }
        progressBar.setVisibility(View.VISIBLE);
    }

    private void updateUIWithBookInfo() {
        // 更新封面图片
        if (!TextUtils.isEmpty(bookInfo.getImageUrl())) {
            Picasso.get()
                    .load(bookInfo.getImageUrl())
                    .placeholder(R.drawable.default_cover)
                    .error(R.drawable.default_cover)
                    .into(ivBookCover);
        }

        // 更新基本信息
        tvBookTitle.setText(bookInfo.getBookName());
        tvBookAuthor.setText(String.format("作者：%s", bookInfo.getAuthor()));
        tvWordCount.setText(String.format("字数：%s", bookInfo.getCount()));
        tvLastChapter.setText(bookInfo.getLastUpdateChapter());

        // 格式化更新时间
        String updateTime = "更新时间：无";
        if (!TextUtils.isEmpty(bookInfo.getLastPublishTime())) {
            try {
                // 尝试解析为Long类型时间戳
                try {
                    long timestamp = Long.parseLong(bookInfo.getLastPublishTime());
                    Date date = new Date(timestamp);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
                    updateTime = "更新时间：" + sdf.format(date);
                } catch (NumberFormatException e) {
                    // 如果不是数字，尝试直接解析日期字符串
                    LogUtil.i(TAG, "尝试解析日期字符串: " + bookInfo.getLastPublishTime());
                    SimpleDateFormat parser = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
                    Date date = parser.parse(bookInfo.getLastPublishTime());
                    updateTime = "更新时间：" + bookInfo.getLastPublishTime();
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "解析时间戳失败: " + e.getMessage());
                // 如果解析失败，直接显示原始字符串
                updateTime = "更新时间：" + bookInfo.getLastPublishTime();
            }
        }
        tvUpdateTime.setText(updateTime);

        // 更新标签
        updateTags();

        // 更新简介
        if (!TextUtils.isEmpty(bookInfo.getAbstractContent())) {
            // 处理简介显示（默认显示简短版本）
            String fullContent = bookInfo.getAbstractContent();
            String shortContent = fullContent;
            if (fullContent.length() > 100) {
                shortContent = fullContent.substring(0, 100) + "...";
            }
            tvBookIntro.setText(shortContent);
            tvBookIntro.setMaxLines(3);
            tvExpandIntro.setVisibility(fullContent.length() > 100 ? View.VISIBLE : View.GONE);
        } else {
            tvExpandIntro.setVisibility(View.GONE);
        }

        // 更新章节列表
        List<ChapterInfo> chapters = bookInfo.getChapterList();
        if (chapters != null && !chapters.isEmpty()) {
            tvChapterCount.setText(String.format("共%d章", chapters.size()));

            // 只显示第一页章节
            updateChapterListWithPage();

            // 设置加载更多按钮可见性
            updateLoadMoreVisibility();
        } else {
            tvLoadMore.setVisibility(View.GONE);
            tvChapterCount.setText("无章节");
        }
    }

    private void updateTags() {
        chipGroupTags.removeAllViews();
        List<String> tags = bookInfo.getTagList();
        if (tags != null && !tags.isEmpty()) {
            for (String tag : tags) {
                if (!TextUtils.isEmpty(tag)) {
                    Chip chip = new Chip(this);
                    chip.setText(tag);
                    chip.setChipBackgroundColorResource(android.R.color.white);
                    chip.setTextColor(ContextCompat.getColor(this, android.R.color.darker_gray));
                    chipGroupTags.addView(chip);
                }
            }
        }
    }

    private void updateChapterListWithPage() {
        List<ChapterInfo> chapters = bookInfo.getChapterList();
        if (chapters == null || chapters.isEmpty()) {
            return;
        }

        int totalChapters = chapters.size();
        int displayCount = Math.min(currentPage * CHAPTERS_PER_PAGE, totalChapters);

        // 创建一个新的列表以避免直接修改原始数据
        List<ChapterInfo> displayChapters = new ArrayList<>(chapters.subList(0, displayCount));

        if (isDescSort) {
            Collections.reverse(displayChapters);
        }

        // 设置到适配器
        chapterAdapter.setChapters(displayChapters);
    }

    private void loadMoreChapters() {
        if (isLoadingMore) {
            return;
        }

        isLoadingMore = true;
        tvLoadMore.setText("加载中...");

        // 模拟加载延迟以提供更好的用户体验
        new Handler().postDelayed(() -> {
            currentPage++;
            updateChapterListWithPage();
            updateLoadMoreVisibility();
            isLoadingMore = false;
            tvLoadMore.setText("点击加载更多");
        }, 300);
    }

    private void updateLoadMoreVisibility() {
        if (bookInfo.getChapterList() != null) {
            int totalChapters = bookInfo.getChapterList().size();
            boolean hasMoreChapters = currentPage * CHAPTERS_PER_PAGE < totalChapters;
            tvLoadMore.setVisibility(hasMoreChapters ? View.VISIBLE : View.GONE);
        } else {
            tvLoadMore.setVisibility(View.GONE);
        }
    }

    private void toggleIntro() {
        showFullIntro = !showFullIntro;
        if (showFullIntro) {
            // 显示完整简介
            tvBookIntro.setText(bookInfo.getAbstractContent());
            tvBookIntro.setMaxLines(Integer.MAX_VALUE);
            tvExpandIntro.setText("收起");
        } else {
            // 显示简短简介
            String fullContent = bookInfo.getAbstractContent();
            String shortContent = fullContent;
            if (fullContent.length() > 100) {
                shortContent = fullContent.substring(0, 100) + "...";
            }
            tvBookIntro.setText(shortContent);
            tvBookIntro.setMaxLines(3);
            tvExpandIntro.setText("展开");
        }
    }

    private void toggleSort() {
        isDescSort = !isDescSort;
        tvSortOrder.setText(isDescSort ? "倒序" : "正序");
        chapterAdapter.reverseChapters();
    }

    /**
     * 注册下载进度广播接收器
     */
    private void registerDownloadProgressReceiver() {
        downloadProgressReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (action == null) return;

                String receivedBookId = intent.getStringExtra("bookId");
                if (receivedBookId == null || !receivedBookId.equals(bookId)) return;

                if (BookshelfFragment.ACTION_DOWNLOAD_PROGRESS.equals(action)) {
                    int progress = intent.getIntExtra("progress", 0);
                    int processedChapters = intent.getIntExtra("processedChapters", 0);
                    int totalChapters = intent.getIntExtra("totalChapters", 0);

                    // 更新进度显示
                    updateDownloadProgress(progress, processedChapters, totalChapters);

                } else if (BookshelfFragment.ACTION_DOWNLOAD_COMPLETE.equals(action)) {
                    // 处理下载完成
                    finishDownload();
                }
            }
        };

        IntentFilter filter = new IntentFilter();
        filter.addAction(BookshelfFragment.ACTION_DOWNLOAD_PROGRESS);
        filter.addAction(BookshelfFragment.ACTION_DOWNLOAD_COMPLETE);

        LocalBroadcastManager.getInstance(this)
                .registerReceiver(downloadProgressReceiver, filter);
    }

    /**
     * 更新下载进度UI
     */
    private void updateDownloadProgress(int progress, int processedChapters, int totalChapters) {
        runOnUiThread(() -> {
            // 显示进度容器
            downloadProgressContainer.setVisibility(View.VISIBLE);

            // 更新进度条
            downloadProgressBar.setMax(totalChapters);
            downloadProgressBar.setProgress(processedChapters);

            // 更新进度文本
            downloadProgressText.setText(String.format("下载进度: %d%% (%d/%d)",
                    progress, processedChapters, totalChapters));

            // 更新书籍对象
            if (bookInfo != null) {
                bookInfo.setDownloading(true);
                bookInfo.setTotalChapters(totalChapters);
                bookInfo.setDownloadedChapters(processedChapters);
            }
        });
    }

    /**
     * 完成下载
     */
    private void finishDownload() {
        runOnUiThread(() -> {
            // 隐藏进度显示
            downloadProgressContainer.setVisibility(View.GONE);

            // 更新书籍状态
            if (bookInfo != null) {
                bookInfo.setDownloading(false);
                bookInfo.setStatus(1); // 已下载状态
                bookInfo.setStatusZh("下载完成");
            }

            // 显示下载完成提示，改用showSuccessToast
            ToastUtil.showSuccessToast(this, "《" + bookName + "》下载完成");
        });
    }

    private void setupListeners() {
        // 展开/收起简介
        tvExpandIntro.setOnClickListener(v -> toggleIntro());

        // 切换排序
        tvSortOrder.setOnClickListener(v -> toggleSort());

        // 加载更多章节
        tvLoadMore.setOnClickListener(v -> loadMoreChapters());

        // 返回顶部
        fabBackToTop.setOnClickListener(v -> {
            // 使用平滑滚动效果返回顶部
            nestedScrollView.smoothScrollTo(0, 0);
            // 点击后隐藏按钮
            fabBackToTop.hide();
        });

        // 监听滚动事件
        nestedScrollView.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener)
                (v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
                    // 当滚动超过500像素时显示返回顶部按钮
                    if (scrollY > 500) {
                        if (fabBackToTop.getVisibility() != View.VISIBLE) {
                            // 使用动画显示
                            fabBackToTop.show();
                        }
                    } else {
                        if (fabBackToTop.getVisibility() == View.VISIBLE) {
                            // 使用动画隐藏
                            fabBackToTop.hide();
                        }
                    }
                });

        // 章节点击
        chapterAdapter.setOnChapterClickListener((chapter, position) -> {
            // 显示加载进度
            progressBar.setVisibility(View.VISIBLE);

            // 此处原本跳转到ReaderActivity，现已去除跳转逻辑
            // ReaderActivity.start(this, bookId, chapter.getLink(), JSON.toJSONString(bookInfo));

            // 隐藏进度条
            progressBar.setVisibility(View.GONE);
        });

        // 下载按钮
        btnDownload.setOnClickListener(v -> {
            // 检查书籍信息是否已加载
            if (bookInfo == null) {
                ToastUtil.showCustomToast(this, "请等待书籍信息加载完成");
                return;
            }
            // 确保bookId已设置
            bookInfo.setBookId(bookId);
            // 优先将bookInfo写入数据库
            bookMapper.insertBook(bookInfo);
            DownloadConfigManager configManager = DownloadConfigManager.getInstance(this);
            configManager.cacheBookInfo(bookInfo);
            Intent intent = new Intent(this, NovelDownloadService.class);
            intent.putExtra("bookId", bookInfo.getBookId());
            CommonCache.putBookName(bookInfo.getBookId(), bookInfo.getBookName());
            startService(intent);
        });

        // 导出按钮
        btnExport.setOnClickListener(v -> {
            // 检查书籍信息是否已加载
            if (bookInfo == null) {
                ToastUtil.showCustomToast(this, "请等待书籍信息加载完成");
                return;
            }

            // 确保bookId已设置
            bookInfo.setBookId(bookId);

            // 使用与书架页一致的导出实现
            com.fanqie.novel.util.BookExportDownloadUtil.exportBook(
                    this,
                    bookInfo,
                    saveFileLauncher,
                    null, // 没有SwipeRefreshLayout
                    new com.fanqie.novel.util.BookExportDownloadUtil.ExportCallback() {
                        @Override
                        public void onExportSuccess(BookInfo updatedBook) {
                            // 导出成功时的操作
                            bookInfo.setStatus(2); // 更新状态为已导出
                        }

                        @Override
                        public void onExportFailed(BookInfo book, String errorMsg) {
                            // 导出失败时的操作
                        }
                    }
            );
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                LogUtil.i(TAG, "存储权限已授予");
            } else {
                LogUtil.w(TAG, "存储权限被拒绝");
                ToastUtil.showCustomToast(this, "需要存储权限才能下载和导出书籍");
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 注销广播接收器
        if (downloadProgressReceiver != null) {
            LocalBroadcastManager.getInstance(this)
                    .unregisterReceiver(downloadProgressReceiver);
        }

        // 关闭线程池，防止内存泄漏
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
} 