package com.fanqie.novel.data.db.mapper;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

public class DownloadConfigMapper extends BaseMapper {
    private static volatile DownloadConfigMapper instance;

    protected DownloadConfigMapper(Context context) {
        super(context);
    }

    public static DownloadConfigMapper getInstance(Context context) {
        if (instance == null) {
            synchronized (DownloadConfigMapper.class) {
                if (instance == null) {
                    instance = new DownloadConfigMapper(context.getApplicationContext());
                }
            }
        }
        return instance;
    }

    // 插入或更新某模式配置
    public void insertOrUpdateConfig(String strategy, String configJson, boolean isCurrent) {
        Cursor cursor = db.rawQuery("SELECT id FROM download_config WHERE strategy=?", new String[]{strategy});
        ContentValues values = new ContentValues();
        values.put("strategy", strategy);
        values.put("config_json", configJson);
        values.put("is_current", isCurrent ? 1 : 0);
        if (cursor.moveToFirst()) {
            int id = cursor.getInt(cursor.getColumnIndexOrThrow("id"));
            db.update("download_config", values, "id=?", new String[]{String.valueOf(id)});
        } else {
            db.insert("download_config", null, values);
        }
        cursor.close();
    }

    // 查询当前激活模式配置
    public ConfigRecord queryCurrentConfig() {
        Cursor cursor = db.rawQuery("SELECT * FROM download_config WHERE is_current=1 LIMIT 1", null);
        ConfigRecord record = null;
        if (cursor.moveToFirst()) {
            String strategy = cursor.getString(cursor.getColumnIndexOrThrow("strategy"));
            String configJson = cursor.getString(cursor.getColumnIndexOrThrow("config_json"));
            record = new ConfigRecord(strategy, configJson, true);
        }
        cursor.close();
        return record;
    }

    // 查询所有模式配置
    public ConfigRecord queryConfigByStrategy(String strategy) {
        Cursor cursor = db.rawQuery("SELECT * FROM download_config WHERE strategy=? LIMIT 1", new String[]{strategy});
        ConfigRecord record = null;
        if (cursor.moveToFirst()) {
            String configJson = cursor.getString(cursor.getColumnIndexOrThrow("config_json"));
            int isCurrent = cursor.getInt(cursor.getColumnIndexOrThrow("is_current"));
            record = new ConfigRecord(strategy, configJson, isCurrent == 1);
        }
        cursor.close();
        return record;
    }

    // 切换当前激活模式
    public void setCurrentStrategy(String strategy) {
        db.execSQL("UPDATE download_config SET is_current=0");
        db.execSQL("UPDATE download_config SET is_current=1 WHERE strategy=?", new Object[]{strategy});
    }

    public static class ConfigRecord {
        public final String strategy;
        public final String configJson;
        public final boolean isCurrent;
        public ConfigRecord(String strategy, String configJson, boolean isCurrent) {
            this.strategy = strategy;
            this.configJson = configJson;
            this.isCurrent = isCurrent;
        }
    }
} 