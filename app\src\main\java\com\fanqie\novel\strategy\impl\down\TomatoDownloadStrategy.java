package com.fanqie.novel.strategy.impl.down;

import android.os.Handler;
import android.os.Looper;

import com.fanqie.novel.data.model.ApiEndpoint;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.data.model.ChapterInfo;
import com.fanqie.novel.strategy.ChapterDownloadStrategy;
import com.fanqie.novel.strategy.DownloadCallback;
import com.fanqie.novel.util.ApiRequestUtil;
import com.fanqie.novel.util.DownloadConfigManager;
import com.fanqie.novel.util.HtmlUtils;
import com.fanqie.novel.util.LogUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;

public class TomatoDownloadStrategy implements ChapterDownloadStrategy {

    private static final Random random = new Random();
    private static final String TAG = "TomatoDownloadStrategy";

    // fanqie_sdk API 的默认配置
    private static final String FANQIE_SDK_URL = "https://sdkapi.fanqieopen.com/open_sdk/reader/content/v1";
    private static final Map<String, Object> DEFAULT_PARAMS = new HashMap<String, Object>() {
        {
            put("sdk_type", "4");
            put("novelsdk_aid", "638505");
        }
    };

    @Override
    public void downloadChapter(ChapterInfo chapter, String bookId, DownloadCallback callback) {
        if (chapter == null || StrUtil.isBlank(chapter.getId())) {
            callback.onFailure(chapter, new Exception("章节信息无效"));
            return;
        }

        LogUtil.d(TAG, "开始下载章节: " + chapter.getId());

        new Thread(() -> {
            try {
                // 随机延迟以避免频繁请求
                Thread.sleep((long) (random.nextDouble() * 400 + 100)); // 0.1-0.5秒随机延迟

                long startTime = System.currentTimeMillis();
                boolean success = downloadFromFanqieSdk(chapter);

                if (success) {
                    long responseTime = System.currentTimeMillis() - startTime;
                    LogUtil.i(TAG, "章节下载成功: " + chapter.getId() +
                            ", 响应时间: " + responseTime + "ms" +
                            ", 内容长度: " + (chapter.getContent() != null ? chapter.getContent().length() : 0));
                    new Handler(Looper.getMainLooper()).post(() -> callback.onSuccess(chapter));
                } else {
                    LogUtil.e(TAG, "章节下载失败: " + chapter.getId());
                    new Handler(Looper.getMainLooper())
                            .post(() -> callback.onFailure(chapter, new Exception("fanqie_sdk API下载失败")));
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LogUtil.e(TAG, "下载线程被中断: " + chapter.getId());
                new Handler(Looper.getMainLooper()).post(() -> callback.onFailure(chapter, new Exception("下载被中断")));
            } catch (Exception e) {
                LogUtil.e(TAG, "下载章节异常: " + chapter.getId(), e);
                new Handler(Looper.getMainLooper()).post(() -> callback.onFailure(chapter, e));
            }
        }).start();
    }

    /**
     * 使用fanqie_sdk API下载章节内容
     * 
     * @param chapter 章节信息
     * @return 是否下载成功
     */
    private boolean downloadFromFanqieSdk(ChapterInfo chapter) {
        try {
            // 创建fanqie_sdk API端点
            ApiEndpoint endpoint = createFanqieSdkEndpoint();
            String userAgent = getRandomUserAgent();

            // 使用ApiRequestUtil发送POST请求
            HttpResponse response = ApiRequestUtil.makeFanqieSdkRequest(endpoint, chapter.getId(), userAgent);

            if (response == null) {
                LogUtil.w(TAG, "fanqie_sdk API响应为空: " + chapter.getId());
                return false;
            }

            try (response) {
                int httpCode = response.getStatus();

                if (httpCode == 200) {
                    String body = response.body();
                    JSON json = JSONUtil.parse(body);

                    // 解析fanqie_sdk响应格式
                    String content = JSONUtil.getByPath(json, "data.content", "");
                    String title = JSONUtil.getByPath(json, "data.title", "");

                    if (!StrUtil.isBlank(content)) {
                        // 处理章节内容
                        String processed = HtmlUtils.cleanHtml(content);

                        // 设置章节信息
                        if (!StrUtil.isBlank(title)) {
                            chapter.setTitle(title);
                        } else if (StrUtil.isBlank(chapter.getTitle())) {
                            chapter.setTitle("第" + chapter.getId() + "章");
                        }

                        chapter.setContent(processed);
                        chapter.setCount(processed.length());

                        LogUtil.d(TAG, "fanqie_sdk API解析成功: " + chapter.getId() +
                                ", 标题: " + chapter.getTitle() +
                                ", 内容长度: " + processed.length());
                        return true;
                    } else {
                        LogUtil.w(TAG, "fanqie_sdk API返回内容为空: " + chapter.getId());
                    }
                } else {
                    LogUtil.w(TAG, "fanqie_sdk API响应非200: " + chapter.getId() + ", code=" + httpCode);
                }
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "fanqie_sdk API请求异常: " + chapter.getId(), e);
        }

        return false;
    }

    /**
     * 创建fanqie_sdk API端点配置
     */
    private ApiEndpoint createFanqieSdkEndpoint() {
        // 默认参数
        Map<String, Object> params = new HashMap<>(DEFAULT_PARAMS);

        // 默认数据
        Map<String, Object> data = new HashMap<>();
        data.put("need_book_info", 1);
        data.put("show_picture", 1);
        data.put("sdk_type", 1);
        // item_id 将在ApiRequestUtil中动态设置

        return new ApiEndpoint(FANQIE_SDK_URL, "fanqie_sdk", null, params, data);
    }

    /**
     * 获取随机User-Agent
     */
    private String getRandomUserAgent() {
        String[] chromeAgents = {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        };
        String[] edgeAgents = {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/119.0.0.0 Safari/537.36"
        };

        // 随机选择浏览器类型
        boolean useChrome = random.nextBoolean();
        String[] agents = useChrome ? chromeAgents : edgeAgents;
        return agents[random.nextInt(agents.length)];
    }

    @Override
    public void getBookInfo(String bookId, BookInfoCallback callback) {
        // TomatoDownloadStrategy专注于fanqie_sdk API的章节下载
        // 书籍信息获取仍使用原有的DownUtil实现，保持兼容性
        try {
            LogUtil.d(TAG, "获取书籍信息: " + bookId);
            BookInfo info = DownloadConfigManager.getInstance(null).getBookInfoById(bookId);
            if (info != null && info.getBookName() != null) {
                LogUtil.d(TAG, "从缓存获取书籍信息成功: " + info.getBookName());
                callback.onSuccess(info);
                return;
            }

            // 如果缓存中没有，尝试使用原有方法获取
            new Thread(() -> {
                try {
                    BookInfo bookInfo = DownloadConfigManager.getInstance(null).getBookInfoById(bookId);
                    if (bookInfo != null && bookInfo.getBookName() != null) {
                        new Handler(Looper.getMainLooper()).post(() -> callback.onSuccess(bookInfo));
                    } else {
                        new Handler(Looper.getMainLooper())
                                .post(() -> callback.onFailure(new Exception("未获取到书籍信息（请先在详情页或书架页加载/缓存）")));
                    }
                } catch (Exception e) {
                    LogUtil.e(TAG, "获取书籍信息异常: " + bookId, e);
                    new Handler(Looper.getMainLooper()).post(() -> callback.onFailure(e));
                }
            }).start();
        } catch (Exception e) {
            LogUtil.e(TAG, "获取书籍信息失败: " + bookId, e);
            callback.onFailure(e);
        }
    }

    @Override
    public boolean supportsBatchDownload() {
        // TomatoDownloadStrategy专注于单章下载，不支持批量下载
        return false;
    }

    @Override
    public void downloadChapters(List<ChapterInfo> chapters, DownloadCallback callback) {
        if (chapters == null || chapters.isEmpty()) {
            LogUtil.w(TAG, "章节列表为空");
            return;
        }

        LogUtil.d(TAG, "开始批量下载章节，数量: " + chapters.size());

        // 使用单章下载方式逐个下载
        for (ChapterInfo chapter : chapters) {
            downloadChapter(chapter, null, callback);

            // 在章节之间添加短暂延迟，避免请求过于频繁
            try {
                Thread.sleep(200); // 200ms延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LogUtil.w(TAG, "批量下载被中断");
                break;
            }
        }

        LogUtil.d(TAG, "批量下载任务提交完成");
    }

    /**
     * 获取策略名称
     */
    public String getStrategyName() {
        return "TomatoDownloadStrategy (fanqie_sdk专用)";
    }

    /**
     * 获取策略描述
     */
    public String getStrategyDescription() {
        return "专门针对fanqie_sdk API优化的下载策略，使用POST请求方式，支持完整的参数传递和错误处理机制";
    }

    /**
     * 测试fanqie_sdk API配置
     * 
     * @return 测试结果字符串
     */
    public String testFanqieSdkConfiguration() {
        StringBuilder result = new StringBuilder("TomatoDownloadStrategy 配置测试:\n");

        try {
            // 测试API端点创建
            ApiEndpoint endpoint = createFanqieSdkEndpoint();
            result.append("API端点URL: ").append(endpoint.getUrl()).append("\n");
            result.append("API名称: ").append(endpoint.getName()).append("\n");

            // 测试参数配置
            Map<String, Object> params = endpoint.getParams();
            result.append("URL参数: ").append(params).append("\n");

            // 测试数据配置
            Map<String, Object> data = endpoint.getData();
            result.append("POST数据: ").append(data).append("\n");

            // 测试User-Agent生成
            String userAgent = getRandomUserAgent();
            result.append("随机User-Agent: ").append(userAgent).append("\n");

            result.append("配置测试通过 ✓");

        } catch (Exception e) {
            result.append("配置测试失败: ").append(e.getMessage());
            LogUtil.e(TAG, "配置测试异常", e);
        }

        return result.toString();
    }
}