<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="2dp"
    android:elevation="2dp"
    app:cardCornerRadius="8dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/tv_api_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="API名称"
            android:textStyle="bold"
            android:textSize="15sp"
            android:textColor="#222222"
            android:layout_marginBottom="4dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_api_url"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="API地址"
                android:textIsSelectable="true"
                android:textColor="#1565C0"
                android:background="@drawable/bg_api_code_block"
                android:padding="8dp"
                android:textSize="13sp"
                android:typeface="monospace"/>

            <ImageButton
                android:id="@+id/btn_copy"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:background="@android:color/transparent"
                android:src="@drawable/ic_content_copy"
                android:tint="#888888"
                android:contentDescription="复制API地址"
                android:layout_marginStart="8dp"/>
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView> 