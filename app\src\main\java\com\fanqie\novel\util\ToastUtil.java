package com.fanqie.novel.util;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;
import com.fanqie.novel.R;

/**
 * Toast工具类
 * 提供自定义Toast显示功能
 */
public class ToastUtil {
    private static final String TAG = "ToastUtil";
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    /**
     * 显示自定义Toast
     *
     * @param context 上下文
     * @param message 消息内容
     */
    public static void showCustomToast(Context context, String message) {
        try {
            LogUtil.d(TAG, "显示Toast消息: " + message);
            // 创建自定义布局
            View toastView = LayoutInflater.from(context)
                    .inflate(R.layout.custom_toast, null);

            // 设置文本
            TextView textView = toastView.findViewById(R.id.toast_text);
            textView.setText(message);

            // 创建并配置Toast
            Toast toast = new Toast(context);
            toast.setDuration(Toast.LENGTH_SHORT);
            toast.setView(toastView);
            toast.setGravity(Gravity.CENTER, 0, 0);
            toast.show();
        } catch (Exception e) {
            // 如果自定义Toast失败，回退到普通Toast
            LogUtil.e(TAG, "显示自定义Toast失败，使用普通Toast", e);
            Toast.makeText(
                    context,
                    message,
                    Toast.LENGTH_SHORT
            ).show();
        }
    }

    /**
     * 显示成功Toast，持续3秒
     *
     * @param context 上下文
     * @param message 消息内容
     */
    public static void showSuccessToast(Context context, String message) {
        try {
            LogUtil.d(TAG, "显示成功Toast消息: " + message);
            // 创建自定义布局
            View toastView = LayoutInflater.from(context)
                    .inflate(R.layout.custom_toast, null);

            // 设置文本
            TextView textView = toastView.findViewById(R.id.toast_text);
            textView.setText(message);

            // 创建并配置Toast
            final Toast toast = new Toast(context);
            toast.setDuration(Toast.LENGTH_LONG); // 使用较长时间显示
            toast.setView(toastView);
            toast.setGravity(Gravity.CENTER, 0, 0);
            toast.show();
            
            // 确保不超过3秒
            mainHandler.postDelayed(toast::cancel, 1500);
        } catch (Exception e) {
            // 如果自定义Toast失败，回退到普通Toast
            LogUtil.e(TAG, "显示自定义Toast失败，使用普通Toast", e);
            Toast toast = Toast.makeText(
                    context,
                    message,
                    Toast.LENGTH_LONG
            );
            toast.show();
            
            // 确保不超过3秒
            mainHandler.postDelayed(toast::cancel, 3000);
        }
    }
} 