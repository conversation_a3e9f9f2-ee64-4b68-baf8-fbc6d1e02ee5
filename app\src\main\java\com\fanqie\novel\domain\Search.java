package com.fanqie.novel.domain;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.util.LogUtil;

import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class Search {
    private static final String TAG = "Search";

    public Search() {
        LogUtil.d(TAG, "初始化Search组件");
    }

    /**
     * 使用JavaScript接口注解，使此方法能够在Web视图中通过JavaScript调用
     * 该方法用于根据关键词搜索书籍信息
     *
     * @param keyword 用户输入的搜索关键词，用于构建搜索请求的URL
     * @return 返回搜索结果的JSON字符串
     */
    public String search(String keyword) {
        LogUtil.i(TAG, "开始搜索: keyword=" + keyword);

        String url = StrUtil.format("https://novel.snssdk.com/api/novel/channel/homepage/search/search/v1/" +
                "?device_platform=android&parent_enterfrom=novel_channel_search.tab.&offset=0&aid=1967&q={}", keyword);
        LogUtil.d(TAG, "搜索URL: " + url);

        try {
            String bookList = null;
            try (HttpResponse response = HttpRequest.get(url).execute()) {
                if (!response.isOk()) {
                    LogUtil.w(TAG, "搜索返回错误码: " + response.getStatus());
                    return "{}";
                }
                bookList = response.body();

                if (StrUtil.isBlank(bookList)) {
                    LogUtil.d(TAG, "搜索结果为空");
                    return "{}";
                }

            }
            JSONArray bookListJson = JSONUtil.parseObj(bookList).getByPath("data.ret_data", JSONArray.class);
            LogUtil.d(TAG, "开始处理搜索结果");
            List<BookInfo> collect = bookListJson.stream()
                    .map(dataDTO -> {
                        JSONObject item = JSONUtil.parseObj(dataDTO);
                        BookInfo bookInfo = new BookInfo();
                        bookInfo.setBookId(item.getStr("book_id"));
                        bookInfo.setBookName(item.getStr("title"));
                        bookInfo.setImageUrl(item.getStr("audio_thumb_uri"));
                        bookInfo.setAuthor(item.getStr("author"));
                        bookInfo.setAbstractContent(item.getStr("abstract"));
                        bookInfo.setTagList(StrUtil.split(item.getStr("category"), ","));
                        bookInfo.setCount(item.getStr("score"));
                        return bookInfo;
                    })
                    .collect(Collectors.toList());

            LogUtil.i(TAG, String.format(Locale.getDefault(), "搜索完成: 关键词=%s, 结果数量=%d", keyword, collect.size()));
            return JSON.toJSONString(collect);

        } catch (Exception e) {
            LogUtil.e(TAG, "搜索过程发生错误", e);
            return "{}";
        }
    }
}
