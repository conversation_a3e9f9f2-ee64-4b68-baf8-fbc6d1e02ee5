package com.fanqie.novel.util;

import android.content.ContentResolver;
import androidx.activity.result.ActivityResult;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.data.model.ChapterInfo;
import org.jetbrains.annotations.NotNull;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fanqie.novel.constants.BookConstants.*;

public class DownUtil {

    private static final String TAG = "DownUtil";

    public static volatile String read_cookie = null;

    private static TomatoRequest tomatoRequest;

    public static void setTomatoRequest(TomatoRequest tomatoRequest) {
        DownUtil.tomatoRequest = tomatoRequest;
    }

    /**
     * 处理文件下载后的回调
     *
     * @param result          Activity结果，包含下载文件的URI
     * @param contentResolver 用于访问内容提供者的内容解析器
     */
    public static void downCallBack(ActivityResult result, ContentResolver contentResolver) {
        FileExportUtil.handleSaveResult(result, contentResolver);
    }


    /**
     * 触发下载
     *
     * @param bookId   书籍id
     * @param filename 文件名
     */
    public static void triggerDownload(String bookId, String filename) {
        try {
            LogUtil.setMdcContext(bookId);
            LogUtil.i(TAG, String.format(Locale.getDefault(), "触发下载: bookId=%s, filename=%s", bookId, filename));
            // TODO: 实现原生下载逻辑（如直接导出文件、更新数据库等）
        } catch (Exception e) {
            LogUtil.e(TAG, "下载过程发生错误", e);
        } finally {
            LogUtil.clearMdcContext();
        }
    }


    /**
     * 通过章节链接下载章节文本
     *
     * @param chapterLink 章节链接
     * @return 下载的章节文本
     */
    public static ChapterInfo downChapterText(String chapterLink) {
        try {
            return getChapterInfoByApi(StrUtil.subAfter(chapterLink, "/", true));
        } catch (Exception e) {
            LogUtil.e(TAG, "下载章节失败: " + chapterLink, e);
            return new ChapterInfo();
        }
    }

    private static ChapterInfo getChapterInfoByApi(String itemId) {
        return tomatoRequest.getDecryptContents(tomatoRequest.batchGet(itemId), itemId);
    }


    /**
     * 通过章节链接下载章节文本
     *
     * @param chapterLink 章节链接
     * @return 下载的章节文本
     */
    public static String getChapterText(String chapterLink) {
        try {
            String linkUrl = BASE_URL + chapterLink;

            HttpResponse execute = HttpUtil.createGet(linkUrl)
                    .headerMap(getRandomHeader(), true)
                    .execute();
            String response = execute.body();
            execute.close();

            Document doc = Jsoup.parse(response);
            Elements pElements = doc.select(".muye-reader-content p");

            StringBuilder content = new StringBuilder();
            for (Element p : pElements) {
                content.append(p.toString());
            }

            String result = strInterpreter(content.toString(), 0);
            LogUtil.i(TAG, String.format(Locale.getDefault(), "章节内容获取完成，长度: %d", result.length()));
            return result;

        } catch (Exception e) {
            LogUtil.e(TAG, "获取章节内容失败: " + chapterLink, e);
            return null;
        }
    }

    /**
     * 字符串处理
     *
     * @param content 字符串
     * @param mode    模式
     * @return 处理后的字符串
     */
    public static String strInterpreter(String content, int mode) {
        Map<String, String> fontDict = mode == 1 ? TITLE_FONT_DICT : DICT_DATA;

        StringBuilder s = new StringBuilder(content.length());
        for (int i = 0; i < content.length(); i++) {
            String index = String.valueOf(content.charAt(i));
            String it = fontDict.get(String.valueOf(index.codePointAt(0)));
            if (it != null) {
                s.append(it);
            } else {
                s.append(index);
            }
        }

        return s.toString();
    }

    /**
     * 随机获取头部信息
     *
     * @return 头部信息
     */
    public static Map<String, String> getRandomHeader() {
        // 模拟随机选择头部信息
        Map<String, String> headers = HEADERS_LIB.get(RandomUtil.randomInt(0, HEADERS_LIB.size()));
        headers.put("cookie", read_cookie);
        return headers;
    }


    /**
     * 获取书籍信息
     * 该方法通过书籍ID获取书籍的详细信息，包括书名、章节信息、标签、摘要、作者和字数
     * 如果书籍信息获取失败或解析无结果，则返回空的JSON对象字符串
     *
     * @param bookId 书籍ID，用于获取书籍文档
     * @return 包含书籍信息的JSON字符串
     */
    public static BookInfo getBookInfo(String bookId) {
        try {
            LogUtil.d(TAG, "获取书籍信息: bookId=" + bookId);
            Document doc = getDocument(bookId);
            List<ChapterInfo> chapters = parseChapterHtmlToList(doc);

            if (!doc.select("h1").isEmpty()) {
                String bookName = doc.select("h1").first().text();
                String abstractText = doc.select(".page-abstract-content p").first().text();
                String tagList = doc.select(".info-label span").stream()
                        .map(Element::text)
                        .collect(Collectors.joining(","));
                String author = doc.select(".author-name-text").first().text();
                String count = doc.select(".info-count-word .detail").first().text() +
                        doc.select(".info-count-word .text").first().text();
                String imageUrl = doc.select(".book-cover-img").first().attr("src");
                String lastUpdateChapter = doc.select(".info-last-title").first().text();
                String lastPublishTime = doc.select(".info-last-time").first().text();

                LogUtil.i(TAG, String.format("书籍信息获取成功: bookName=%s, author=%s",
                        bookName, author));
                BookInfo bookInfo = new BookInfo(bookName, chapters, tagList, abstractText, author,
                        count, imageUrl, lastUpdateChapter, lastPublishTime);
                bookInfo.setBookId(bookId);
                bookInfo.setStatus(0);
                return bookInfo;
            } else {
                LogUtil.w(TAG, "未找到书籍信息: bookId=" + bookId);
                return new BookInfo();
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "获取书籍信息失败: bookId=" + bookId, e);
            return new BookInfo();
        }
    }

    /**
     * 解析章节html
     *
     * @param doc html
     * @return 章节字典
     */
    @NotNull
    private static List<ChapterInfo> parseChapterHtmlToList(Document doc) {
        List<ChapterInfo> chapters = new ArrayList<>();
        // 初始化章节字典，用于存储章节标题和链接
        Elements aElements = doc.select(".chapter div a");
        int sort = 0;
        for (Element a : aElements) {
            chapters.add(new ChapterInfo(a.text(), a.attr("href"), ++sort));
        }
        return chapters;
    }

    /**
     * 获取书籍页面
     *
     * @param bookId 书籍id
     * @return 解析后的html
     */
    @NotNull
    private static Document  getDocument(String bookId) {
        String url = BASE_URL + "/page/" + bookId;
        HttpResponse execute = HttpUtil.createGet(url).execute();
        String response = execute.body();
        execute.close();
        return Jsoup.parse(response);
    }


    /**
     * 获取章节字典
     *
     * @param bookId 书籍id
     * @return 章节字典
     */
    public static List<ChapterInfo> getChapterList(String bookId) {
        Document doc = getDocument(bookId);
        List<ChapterInfo> chapters = parseChapterHtmlToList(doc);
        for (ChapterInfo chapter : chapters) {
            chapter.setBookId(bookId);
        }
        return chapters;
    }

}
