<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <set>
            <objectAnimator
                android:duration="150"
                android:propertyName="translationZ"
                android:valueTo="8dp"
                android:valueType="floatType" />
            <objectAnimator
                android:duration="150"
                android:propertyName="elevation"
                android:valueTo="8dp"
                android:valueType="floatType" />
        </set>
    </item>
    <!-- 普通状态 -->
    <item>
        <set>
            <objectAnimator
                android:duration="150"
                android:propertyName="translationZ"
                android:valueTo="0dp"
                android:valueType="floatType" />
            <objectAnimator
                android:duration="150"
                android:propertyName="elevation"
                android:valueTo="4dp"
                android:valueType="floatType" />
        </set>
    </item>
</selector> 