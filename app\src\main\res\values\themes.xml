<resources>
    <!-- Base application theme. -->
    <style name="Theme.Fanqienovel" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_66b</item>
        <item name="colorPrimaryVariant">@color/purple_66b</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/purple_66b</item>
        <!-- Customize your theme here. -->
    </style>
    <style name="Theme.Fanqienovel.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="colorPrimary">@color/purple_66b</item>
        <item name="colorPrimaryVariant">@color/purple_66b</item>
        <item name="android:statusBarColor">@color/purple_66b</item>
    </style>
    <style name="Theme.Fanqienovel.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="Theme.Fanqienovel.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
    
    <!-- 更新为Material Components兼容的主题 -->
    <style name="NoActionBarTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/purple_66b</item>
        <item name="colorPrimaryVariant">@color/purple_66b</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">@color/purple_66b</item>
    </style>
</resources>