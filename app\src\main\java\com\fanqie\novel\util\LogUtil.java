package com.fanqie.novel.util;

import android.content.Context;
import android.util.Log;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 日志工具类
 * 提供统一的日志记录接口，支持文件输出和控制台输出
 * 主要功能：
 * - 按日期生成日志文件
 * - 支持多种日志级别
 * - 异步写入日志文件
 * - 自动清理过期日志
 * - 支持MDC功能
 *
 * <AUTHOR>
 */
public class LogUtil {
    private static final String TAG = "LogUtil";
    private static final ExecutorService logExecutor = Executors.newSingleThreadExecutor();
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.CHINA);
    private static final SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.CHINA);
    // ThreadLocal for MDC
    private static final ThreadLocal<String> mdcContext = new ThreadLocal<>();
    private static File logDir;

    /**
     * 初始化日志系统
     * 创建日志目录并设置基本参数
     *
     * @param context 应用上下文，用于获取外部存储目录
     */
    public static void init(Context context) {
        logDir = new File(context.getExternalFilesDir(null), "logs");
        if (!logDir.exists() && !logDir.mkdirs()) {
            Log.e(TAG, "创建日志目录失败");
        }
        cleanOldLogs();
    }

    // Clear MDC context
    public static void clearMdcContext() {
        mdcContext.remove();
    }

    /**
     * 记录信息级别日志
     *
     * @param tag     日志标签
     * @param message 日志消息
     */
    public static void i(String tag, String message) {
        Log.i(tag, formatLogMessage(message));
        writeToFile("INFO", tag, message);
    }

    /**
     * 记录调试级别日志
     *
     * @param tag     日志标签
     * @param message 日志消息
     */
    public static void d(String tag, String message) {
        Log.d(tag, formatLogMessage(message));
        writeToFile("DEBUG", tag, message);
    }

    /**
     * 记录警告级别日志
     *
     * @param tag     日志标签
     * @param message 日志消息
     */
    public static void w(String tag, String message) {
        Log.w(tag, formatLogMessage(message));
        writeToFile("WARN", tag, message);
    }

    /**
     * 记录错误级别日志
     *
     * @param tag     日志标签
     * @param message 日志消息
     */
    public static void e(String tag, String message) {
        Log.e(tag, formatLogMessage(message));
        writeToFile("ERROR", tag, message);
    }

    /**
     * 记录错误级别日志，包含异常堆栈
     *
     * @param tag     日志标签
     * @param message 日志消息
     * @param e       异常对象
     */
    public static void e(String tag, String message, Throwable e) {
        Log.e(tag, formatLogMessage(message), e);
        writeToFile("ERROR", tag, message + "\n" + Log.getStackTraceString(e));
    }

    /**
     * 记录详细级别日志
     *
     * @param tag     日志标签
     * @param message 日志消息
     */
    public static void v(String tag, String message) {
        Log.v(tag, formatLogMessage(message));
        writeToFile("VERBOSE", tag, message);
    }

    /**
     * 获取当前MDC上下文
     *
     * @return 当前MDC上下文
     */
    public static String getMdcContext() {
        return mdcContext.get();
    }

    // Set MDC context
    public static void setMdcContext(String context) {
        mdcContext.set(context);
    }

    /**
     * 格式化日志消息
     *
     * @param message 日志消息
     * @return 格式化后的日志消息
     */
    private static String formatLogMessage(String message) {
        String context = getMdcContext();
        return (context != null ? "[traceId: " + context + "] " : "") + message;
    }

    /**
     * 将日志写入文件
     * 异步执行写入操作，避免阻塞主线程
     *
     * @param level   日志级别
     * @param tag     日志标签
     * @param message 日志消息
     */
    private static void writeToFile(String level, String tag, String message) {
        if (logDir == null) {
            return;
        }

        logExecutor.execute(() -> {
            String timestamp = timeFormat.format(new Date());
            String logMessage = String.format(Locale.getDefault(), "%s [%s] %s: %s\n", timestamp, level, tag, message);
            File logFile = new File(logDir, "log_" + dateFormat.format(new Date()) + ".txt");

            try (BufferedWriter writer = new BufferedWriter(
                    new OutputStreamWriter(new FileOutputStream(logFile, true),
                            StandardCharsets.UTF_8))) {
                writer.write(logMessage);
                writer.flush();
            } catch (IOException e) {
                Log.e(TAG, "写入日志文件失败", e);
            }
        });
    }

    /**
     * 清理过期的日志文件
     * 只保留近3天的日志文件
     */
    public static void cleanOldLogs() {
        if (logDir == null) {
            return;
        }

        logExecutor.execute(() -> {
            File[] logFiles = logDir.listFiles((dir, name) -> name.startsWith("log_"));
            if (logFiles == null) {
                return;
            }

            long now = System.currentTimeMillis();
            long keepTimeMillis = 3 * 24 * 60 * 60 * 1000L; // 3天的毫秒数

            for (File logFile : logFiles) {
                if (now - logFile.lastModified() > keepTimeMillis) {
                    if (!logFile.delete()) {
                        Log.w(TAG, "删除旧日志文件失败: " + logFile.getName());
                    } else {
                        Log.d(TAG, "删除旧日志文件成功: " + logFile.getName());
                    }
                }
            }
        });
    }

    /**
     * 关闭日志系统
     * 清理资源并等待所有日志写入完成
     */
    public static void shutdown() {
        logExecutor.shutdown();
    }
} 