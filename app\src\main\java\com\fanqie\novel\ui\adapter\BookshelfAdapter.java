package com.fanqie.novel.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.fanqie.novel.R;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.util.ImageLoader;

import java.util.List;

public class BookshelfAdapter extends RecyclerView.Adapter<BookshelfAdapter.BookViewHolder> {

    private final Context context;
    private final List<BookInfo> bookList;
    private final BookItemClickListener listener;

    public BookshelfAdapter(Context context, List<BookInfo> bookList, BookItemClickListener listener) {
        this.context = context;
        this.bookList = bookList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public BookViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_book, parent, false);
        return new BookViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull BookViewHolder holder, int position) {
        BookInfo book = bookList.get(position);

        // 设置书籍信息
        holder.titleText.setText(book.getBookName());

        // 设置作者信息
        holder.authorText.setText("作者：" + book.getAuthor());

        // 设置字数信息
        holder.wordCountText.setText("字数：" + book.getCount());

        // 设置状态信息
        String status = book.getStatusZh();
        if (book.getChapterCount() != null && !book.getChapterCount().isEmpty()) {
            // 如果是下载失败状态，显示进度信息
            if ("下载失败".equals(book.getStatusZh())) {
                status += " (" + book.getChapterCount() + ")";
            } else {
                status += " " + book.getChapterCount();
            }
        }
        holder.statusText.setText("状态：" + status);

        // 设置最新章节
        holder.lastChapterText.setText(book.getLastUpdateChapter());

        // 加载封面图片
        if (book.getImageUrl() != null && !book.getImageUrl().isEmpty()) {
            ImageLoader.loadImage(context, book.getImageUrl(), holder.coverImage);
        } else {
            holder.coverImage.setImageResource(R.drawable.ic_bookshelf);
        }

        // 处理下载进度显示
        updateDownloadProgress(holder, book);

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onBookItemClick(book);
            }
        });

        holder.btnDownload.setOnClickListener(v -> {
            if (listener != null) {
                listener.onDownloadClick(book);
            }
        });

        holder.btnExport.setOnClickListener(v -> {
            if (listener != null) {
                listener.onExportClick(book);
            }
        });

        holder.btnDelete.setOnClickListener(v -> {
            if (listener != null) {
                listener.onDeleteClick(book);
            }
        });
    }

    @Override
    public void onBindViewHolder(@NonNull BookViewHolder holder, int position, @NonNull List<Object> payloads) {
        if (payloads.isEmpty()) {
            // 如果没有携带数据，执行完整的绑定
            super.onBindViewHolder(holder, position, payloads);
        } else {
            // 只更新进度条，不重新加载图片和其他内容
            BookInfo book = bookList.get(position);
            updateDownloadProgress(holder, book);
        }
    }

    /**
     * 只更新下载进度显示，抽取为单独方法
     */
    private void updateDownloadProgress(BookViewHolder holder, BookInfo book) {
        if (book.isDownloading()) {
            holder.downloadProgressContainer.setVisibility(View.VISIBLE);
            holder.progressBar.setProgress(book.getDownloadProgress());
            holder.progressText.setText(String.format("下载进度: %d%% (%d/%d)",
                    book.getDownloadProgress(),
                    book.getDownloadedChapters(),
                    book.getTotalChapters()));
        } else {
            holder.downloadProgressContainer.setVisibility(View.GONE);
        }
    }

    @Override
    public int getItemCount() {
        return bookList.size();
    }

    // ViewHolder类
    static class BookViewHolder extends RecyclerView.ViewHolder {
        ImageView coverImage;
        TextView titleText;
        TextView authorText;
        TextView wordCountText;
        TextView statusText;
        TextView lastChapterText;
        Button btnDownload;
        Button btnExport;
        Button btnDelete;
        View downloadProgressContainer;
        ProgressBar progressBar;
        TextView progressText;

        BookViewHolder(@NonNull View itemView) {
            super(itemView);
            coverImage = itemView.findViewById(R.id.book_cover);
            titleText = itemView.findViewById(R.id.book_title);
            authorText = itemView.findViewById(R.id.book_author);
            wordCountText = itemView.findViewById(R.id.book_word_count);
            statusText = itemView.findViewById(R.id.book_status);
            lastChapterText = itemView.findViewById(R.id.book_last_chapter);
            btnDownload = itemView.findViewById(R.id.btn_download);
            btnExport = itemView.findViewById(R.id.btn_export);
            btnDelete = itemView.findViewById(R.id.btn_delete);

            // 初始化进度条相关视图
            downloadProgressContainer = itemView.findViewById(R.id.download_progress_container);
            progressBar = itemView.findViewById(R.id.download_progress_bar);
            progressText = itemView.findViewById(R.id.download_progress_text);
        }
    }

    // 接口，用于处理项目点击事件
    public interface BookItemClickListener {
        void onBookItemClick(BookInfo book);

        void onDownloadClick(BookInfo book);

        void onExportClick(BookInfo book);

        void onDeleteClick(BookInfo book);
    }
}