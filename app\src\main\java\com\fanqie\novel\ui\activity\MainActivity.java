package com.fanqie.novel.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.fanqie.novel.R;
import com.fanqie.novel.constants.DownloadStrategyType;
import com.fanqie.novel.data.db.mapper.TomatoVariableMapper;
import com.fanqie.novel.data.model.TomatoVariable;
import com.fanqie.novel.strategy.impl.down.OpenSourceDownloadStrategy;
import com.fanqie.novel.ui.fragment.BookshelfFragment;
import com.fanqie.novel.ui.fragment.SearchFragment;
import com.fanqie.novel.util.DownUtil;
import com.fanqie.novel.util.DownloadConfigManager;
import com.fanqie.novel.util.LogUtil;
import com.fanqie.novel.util.TomatoRequest;
import com.google.android.material.bottomnavigation.BottomNavigationView;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final String TAG_SEARCH = "search";
    private static final String TAG_BOOKSHELF = "bookshelf";
    
    private Toolbar toolbar;
    private SearchFragment searchFragment;
    private BookshelfFragment bookshelfFragment;
    private Fragment activeFragment;
    private BottomNavigationView bottomNavigationView;
    private FragmentManager fragmentManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        LogUtil.d(TAG, "创建MainActivity");
        
        // 初始化工具类
        LogUtil.init(this);
        initTomatoRequest();
        
        // 初始化Toolbar
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        // 不显示标题，使用自定义TextView显示
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayShowTitleEnabled(false);
            getSupportActionBar().setDisplayHomeAsUpEnabled(false);
        }
        
        // 设置按钮点击事件
        findViewById(R.id.btn_settings).setOnClickListener(v -> {
            Intent intent = new Intent(this, SettingsActivity.class);
            startActivity(intent);
        });
        
        // 初始化Fragment管理器
        fragmentManager = getSupportFragmentManager();
        
        if (savedInstanceState == null) {
            // 首次创建，newInstance并add
            searchFragment = SearchFragment.newInstance();
            bookshelfFragment = BookshelfFragment.newInstance();
            fragmentManager.beginTransaction()
                    .add(R.id.fragment_container, bookshelfFragment, TAG_BOOKSHELF)
                    .hide(bookshelfFragment)
                    .add(R.id.fragment_container, searchFragment, TAG_SEARCH)
                    .commit();
            activeFragment = searchFragment;
        } else {
            // Activity重建，恢复Fragment引用
            Fragment sf = fragmentManager.findFragmentByTag(TAG_SEARCH);
            Fragment bf = fragmentManager.findFragmentByTag(TAG_BOOKSHELF);
            if (sf instanceof SearchFragment) {
                searchFragment = (SearchFragment) sf;
            } else {
                LogUtil.e(TAG, "searchFragment恢复失败，重新创建");
                searchFragment = SearchFragment.newInstance();
                fragmentManager.beginTransaction().add(R.id.fragment_container, searchFragment, TAG_SEARCH).commit();
            }
            if (bf instanceof BookshelfFragment) {
                bookshelfFragment = (BookshelfFragment) bf;
            } else {
                LogUtil.e(TAG, "bookshelfFragment恢复失败，重新创建");
                bookshelfFragment = BookshelfFragment.newInstance();
                fragmentManager.beginTransaction().add(R.id.fragment_container, bookshelfFragment, TAG_BOOKSHELF).commit();
            }
            // 恢复activeFragment
            if (searchFragment != null && !searchFragment.isHidden()) {
                activeFragment = searchFragment;
            } else if (bookshelfFragment != null) {
                activeFragment = bookshelfFragment;
            } else {
                activeFragment = searchFragment;
            }
        }
        
        // 设置底部导航
        bottomNavigationView = findViewById(R.id.bottom_navigation);
        bottomNavigationView.setOnItemSelectedListener(this::onNavigationItemSelected);
        
        // 策略为PYTHON_LIKE时自动拉取API端点
        DownloadConfigManager configManager = DownloadConfigManager.getInstance(this);
        if (configManager.getCurrentStrategy() == DownloadStrategyType.PYTHON_LIKE && !configManager.isApiFetched()) {
            String serverUrl = configManager.getServerUrl();
            String authToken = configManager.getAuthToken();
            if (serverUrl != null && !serverUrl.isEmpty() && authToken != null && !authToken.isEmpty()) {
                new OpenSourceDownloadStrategy().fetchApiEndpointsFromServer(serverUrl, authToken, (success, endpoints, errorMsg) -> {
                    if (success) {
                        configManager.setApiEndpoints(endpoints);
                    } else {
                        LogUtil.e(TAG, "API端点获取失败: " + errorMsg);
                    }
                });
            }
        }
    }
    
    private void initTomatoRequest() {
        // 初始化 Mappers
        TomatoVariableMapper tomatoVariableMapper = TomatoVariableMapper.getInstance(this);
        TomatoVariable tomatoVariable = tomatoVariableMapper.queryTomatoVariable();
        if (tomatoVariable == null){
            tomatoVariable = new TomatoVariable("4427064614339001", "4427064614334905", "1967", "62532");
            tomatoVariableMapper.insertTomatoVariable("4427064614339001", "4427064614334905", "1967", "62532");
        }
        TomatoRequest tomatoRequest = new TomatoRequest(tomatoVariable);
        // 设置到 DownUtil
        DownUtil.setTomatoRequest(tomatoRequest);
    }
    
    private void setToolbarTitle(String title) {
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(title);
        }
        TextView toolbarTitle = findViewById(R.id.toolbar_title);
        if (toolbarTitle != null) {
            toolbarTitle.setText(title);
        }
    }

    private boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();
        
        if (itemId == R.id.navigation_search) {
            // 切换到搜索Fragment
            if (activeFragment != searchFragment) {
                fragmentManager.beginTransaction()
                        .hide(activeFragment)
                        .show(searchFragment)
                        .commit();
                activeFragment = searchFragment;
            }
            return true;
        } else if (itemId == R.id.navigation_bookshelf) {
            // 切换到书架Fragment
            if (activeFragment != bookshelfFragment) {
                fragmentManager.beginTransaction()
                        .hide(activeFragment)
                        .show(bookshelfFragment)
                        .commit();
                activeFragment = bookshelfFragment;
            }
            return true;
        }
        
        return false;
    }

    @Override
    public void onBackPressed() {
        // 处理返回事件
        if (activeFragment == searchFragment && searchFragment.canGoBack()) {
            // 如果SearchFragment可以后退，则执行后退操作
            searchFragment.goBack();
        } else if (activeFragment == searchFragment) {
            // 搜索页面但无法后退，切换到书架页面
            bottomNavigationView.setSelectedItemId(R.id.navigation_bookshelf);
        } else {
            // 其他情况，执行默认的后退操作
            super.onBackPressed();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        MenuItem settingsItem = menu.findItem(R.id.action_settings);
        
        // 确保图标显示在工具栏上
        settingsItem.setTitle("设置");
        settingsItem.setIcon(R.drawable.ic_settings);
        settingsItem.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS);
        
        // 记录创建菜单
        LogUtil.d(TAG, "创建菜单，设置图标: " + settingsItem.getIcon());
        
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_settings) {
            // 打开设置页面
            Intent intent = new Intent(this, SettingsActivity.class);
            startActivity(intent);
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
