import java.text.SimpleDateFormat

plugins {
    id 'com.android.application'
}

android {
    namespace 'com.fanqie.novel'
    compileSdk 33

    defaultConfig {
        applicationId "com.fanqie.novel"
        minSdk 24
        targetSdk 33
        versionCode 4
        versionName "5.0." + (new SimpleDateFormat("MMddHHmmss")).format(new Date())

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            // 使用调试签名，避免密钥库密码问题
            signingConfig signingConfigs.debug
        }
        debug {
            // 确保debug构建使用默认的调试签名
            signingConfig signingConfigs.debug
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation 'androidx.transition:transition:1.4.1'
    implementation 'androidx.navigation:navigation-fragment:2.4.1'
    implementation 'androidx.navigation:navigation-ui:2.4.1'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'cn.hutool:hutool-all:5.8.32'
    implementation 'com.alibaba:fastjson:1.2.83'
    implementation 'org.jsoup:jsoup:1.15.3'

}