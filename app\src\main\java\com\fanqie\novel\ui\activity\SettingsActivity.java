package com.fanqie.novel.ui.activity;

import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.fanqie.novel.R;
import com.fanqie.novel.constants.DownloadStrategyType;
import com.fanqie.novel.strategy.ISettingsStrategy;
import com.fanqie.novel.strategy.impl.setting.LegacySettingsStrategy;
import com.fanqie.novel.strategy.impl.setting.OpenSourceSettingsStrategy;
import com.fanqie.novel.ui.adapter.ApiGroupAdapter;
import com.fanqie.novel.util.DownloadConfigManager;
import com.fanqie.novel.util.ToastUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SettingsActivity extends AppCompatActivity {
    private static final String TAG = "SettingsActivity";
    private EditText etInstallId, etServerDeviceId, etAid, etUpdateVersionCode, etBatchSize, etBatchName;
    private RadioButton rbPythonLike;
    private EditText etServerUrl, etAuthToken;
    private DownloadConfigManager configManager;
    private View tilBatchSize;
    private View groupLegacyConfig, groupPythonLikeConfig;
    private ISettingsStrategy currentStrategy;
    private ApiGroupAdapter apiGroupAdapter;
    private final List<Object> apiGroupDataList = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        // 先获取分组容器并立即刷新显示状态
        groupLegacyConfig = findViewById(R.id.group_legacy_config);
        groupPythonLikeConfig = findViewById(R.id.group_python_like_config);
        configManager = DownloadConfigManager.getInstance(this);
        // 初始化策略
        updateCurrentStrategy();
        updatePythonLikeFieldsVisibility();

        // 再初始化其它控件
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("设置");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        etInstallId = findViewById(R.id.et_install_id);
        etServerDeviceId = findViewById(R.id.et_server_device_id);
        etAid = findViewById(R.id.et_aid);
        etUpdateVersionCode = findViewById(R.id.et_update_version_code);
        Button btnSave = findViewById(R.id.btn_save);
        etServerUrl = findViewById(R.id.et_server_url);
        etAuthToken = findViewById(R.id.et_auth_token);
        RadioGroup rgDownloadStrategy = findViewById(R.id.rg_download_strategy);
        RadioButton rbLegacy = findViewById(R.id.rb_legacy);
        rbPythonLike = findViewById(R.id.rb_python_like);
        etBatchSize = findViewById(R.id.et_batch_size);
        tilBatchSize = findViewById(R.id.til_batch_size);
        etBatchName = findViewById(R.id.et_batch_name);
        RecyclerView rvApiGroup = findViewById(R.id.rv_api_group);
        rvApiGroup.setLayoutManager(new LinearLayoutManager(this));
        apiGroupAdapter = new ApiGroupAdapter(this, apiGroupDataList);
        rvApiGroup.setAdapter(apiGroupAdapter);

        loadCurrentSettings();

        if (configManager.getCurrentStrategy() == DownloadStrategyType.PYTHON_LIKE) {
            rbPythonLike.setChecked(true);
        } else {
            rbLegacy.setChecked(true);
        }

        rgDownloadStrategy.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.rb_legacy) {
                configManager.setCurrentStrategy(DownloadStrategyType.LEGACY);
            } else {
                configManager.setCurrentStrategy(DownloadStrategyType.PYTHON_LIKE);
            }
            updateCurrentStrategy();
            updatePythonLikeFieldsVisibility();
            loadCurrentSettings();
        });

        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveSettingsByStrategy();
            }
        });
    }

    private void updateCurrentStrategy() {
        DownloadStrategyType type = configManager.getCurrentStrategy();
        if (type == DownloadStrategyType.PYTHON_LIKE) {
            currentStrategy = new OpenSourceSettingsStrategy();
        } else {
            currentStrategy = new LegacySettingsStrategy();
        }
    }

    private void loadCurrentSettings() {
        currentStrategy.getConfig(this, new ISettingsStrategy.Callback() {
            @Override
            public void onSuccess(Map<String, Object> config) {
                fillUiFromConfig(config);
            }

            @Override
            public void onFailure(String errorMsg) {
                ToastUtil.showCustomToast(SettingsActivity.this, errorMsg);
            }
        });
        updateApiEndpointViews();
        updatePythonLikeFieldsVisibility();
    }

    private void fillUiFromConfig(Map<String, Object> config) {
        DownloadStrategyType cur = configManager.getCurrentStrategy();
        if (cur == DownloadStrategyType.LEGACY) {
            etInstallId.setText((String) config.getOrDefault("installId", ""));
            etServerDeviceId.setText((String) config.getOrDefault("serverDeviceId", ""));
            etAid.setText((String) config.getOrDefault("aid", ""));
            etUpdateVersionCode.setText((String) config.getOrDefault("updateVersionCode", ""));
        } else {
            etServerUrl.setText((String) config.getOrDefault("server_url", ""));
            etAuthToken.setText((String) config.getOrDefault("auth_token", ""));
            etBatchSize.setText(String.valueOf(config.getOrDefault("max_batch_size", 10)));
            etBatchName.setText((String) config.getOrDefault("name", ""));
        }
    }

    private void saveSettingsByStrategy() {
        DownloadStrategyType strategy = rbPythonLike.isChecked() ? DownloadStrategyType.PYTHON_LIKE : DownloadStrategyType.LEGACY;
        Map<String, Object> values = new java.util.HashMap<>();
        if (strategy == DownloadStrategyType.LEGACY) {
            values.put("installId", etInstallId.getText().toString().trim());
            values.put("serverDeviceId", etServerDeviceId.getText().toString().trim());
            values.put("aid", etAid.getText().toString().trim());
            values.put("updateVersionCode", etUpdateVersionCode.getText().toString().trim());
        } else {
            values.put("server_url", etServerUrl.getText().toString().trim());
            values.put("auth_token", etAuthToken.getText().toString().trim());
            values.put("max_batch_size", etBatchSize.getText().toString().trim());
            values.put("name", etBatchName.getText().toString().trim());
        }
        currentStrategy.updateConfig(this, values, new ISettingsStrategy.Callback() {
            @Override
            public void onSuccess(Map<String, Object> config) {
                currentStrategy.onSuccess(SettingsActivity.this);
            }

            @Override
            public void onFailure(String errorMsg) {
                currentStrategy.onFailure(SettingsActivity.this, errorMsg);
            }
        });
    }

    private void updatePythonLikeFieldsVisibility() {
        boolean isPythonLike = configManager.getCurrentStrategy() == DownloadStrategyType.PYTHON_LIKE;
        if (groupLegacyConfig != null) groupLegacyConfig.setVisibility(isPythonLike ? View.GONE : View.VISIBLE);
        if (groupPythonLikeConfig != null) groupPythonLikeConfig.setVisibility(isPythonLike ? View.VISIBLE : View.GONE);
        if (tilBatchSize != null) tilBatchSize.setVisibility(isPythonLike ? View.VISIBLE : View.GONE);
    }

    private void updateApiEndpointViews() {
        // 新实现：组装分组数据，始终平铺所有分组和条目
        apiGroupDataList.clear();
        List<ApiGroupAdapter.ApiEntry> batchList = new ArrayList<>();
        List<ApiGroupAdapter.ApiEntry> singleList = new ArrayList<>();
        for (com.fanqie.novel.data.model.ApiEndpoint endpoint : configManager.getApiEndpoints()) {
            if (endpoint.getName() != null && endpoint.getName().contains("批量")) {
                batchList.add(new ApiGroupAdapter.ApiEntry(null, endpoint.getUrl()));
            } else {
                singleList.add(new ApiGroupAdapter.ApiEntry(endpoint.getName(), endpoint.getUrl()));
            }
        }
        ApiGroupAdapter.ApiGroup batchGroup = new ApiGroupAdapter.ApiGroup("批量下载API", batchList);
        ApiGroupAdapter.ApiGroup singleGroup = new ApiGroupAdapter.ApiGroup("单章下载API", singleList);
        apiGroupDataList.add(batchGroup);
        apiGroupDataList.addAll(batchGroup.entryList);
        apiGroupDataList.add(singleGroup);
        apiGroupDataList.addAll(singleGroup.entryList);
        apiGroupAdapter.notifyDataSetChanged();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
} 