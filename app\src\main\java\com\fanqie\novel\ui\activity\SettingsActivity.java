package com.fanqie.novel.ui.activity;

import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.fanqie.novel.R;
import com.fanqie.novel.constants.DownloadStrategyType;
import com.fanqie.novel.strategy.ISettingsStrategy;
import com.fanqie.novel.strategy.impl.setting.LegacySettingsStrategy;
import com.fanqie.novel.strategy.impl.setting.OpenSourceSettingsStrategy;
import com.fanqie.novel.ui.adapter.ApiGroupAdapter;
import com.fanqie.novel.util.DownloadConfigManager;
import com.fanqie.novel.util.JsonConfigParser;
import com.fanqie.novel.util.ToastUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SettingsActivity extends AppCompatActivity {
    private static final String TAG = "SettingsActivity";
    private EditText etInstallId, etServerDeviceId, etAid, etUpdateVersionCode, etBatchSize, etBatchName;
    private RadioButton rbPythonLike;
    private EditText etServerUrl, etAuthToken;
    private DownloadConfigManager configManager;
    private View tilBatchSize;
    private View groupLegacyConfig, groupPythonLikeConfig;
    private ISettingsStrategy currentStrategy;
    private ApiGroupAdapter apiGroupAdapter;
    private final List<Object> apiGroupDataList = new ArrayList<>();

    // JSON配置相关控件
    private EditText etJsonConfig;
    private Button btnValidateJson, btnPreviewJson, btnLoadJsonExample, btnTestJsonConfig;
    private TextView tvJsonStatus;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        // 先获取分组容器并立即刷新显示状态
        groupLegacyConfig = findViewById(R.id.group_legacy_config);
        groupPythonLikeConfig = findViewById(R.id.group_python_like_config);
        configManager = DownloadConfigManager.getInstance(this);
        // 初始化策略
        updateCurrentStrategy();
        updatePythonLikeFieldsVisibility();

        // 再初始化其它控件
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("设置");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        etInstallId = findViewById(R.id.et_install_id);
        etServerDeviceId = findViewById(R.id.et_server_device_id);
        etAid = findViewById(R.id.et_aid);
        etUpdateVersionCode = findViewById(R.id.et_update_version_code);
        Button btnSave = findViewById(R.id.btn_save);
        etServerUrl = findViewById(R.id.et_server_url);
        etAuthToken = findViewById(R.id.et_auth_token);
        RadioGroup rgDownloadStrategy = findViewById(R.id.rg_download_strategy);
        RadioButton rbLegacy = findViewById(R.id.rb_legacy);
        rbPythonLike = findViewById(R.id.rb_python_like);
        etBatchSize = findViewById(R.id.et_batch_size);
        tilBatchSize = findViewById(R.id.til_batch_size);
        etBatchName = findViewById(R.id.et_batch_name);
        RecyclerView rvApiGroup = findViewById(R.id.rv_api_group);
        rvApiGroup.setLayoutManager(new LinearLayoutManager(this));
        apiGroupAdapter = new ApiGroupAdapter(this, apiGroupDataList);
        rvApiGroup.setAdapter(apiGroupAdapter);

        // 初始化JSON配置相关控件
        initJsonConfigViews();

        loadCurrentSettings();

        if (configManager.getCurrentStrategy() == DownloadStrategyType.PYTHON_LIKE) {
            rbPythonLike.setChecked(true);
        } else {
            rbLegacy.setChecked(true);
        }

        rgDownloadStrategy.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.rb_legacy) {
                configManager.setCurrentStrategy(DownloadStrategyType.LEGACY);
            } else {
                configManager.setCurrentStrategy(DownloadStrategyType.PYTHON_LIKE);
            }
            updateCurrentStrategy();
            updatePythonLikeFieldsVisibility();
            loadCurrentSettings();
        });

        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveSettingsByStrategy();
            }
        });
    }

    private void updateCurrentStrategy() {
        DownloadStrategyType type = configManager.getCurrentStrategy();
        if (type == DownloadStrategyType.PYTHON_LIKE) {
            currentStrategy = new OpenSourceSettingsStrategy();
        } else {
            currentStrategy = new LegacySettingsStrategy();
        }
    }

    private void loadCurrentSettings() {
        currentStrategy.getConfig(this, new ISettingsStrategy.Callback() {
            @Override
            public void onSuccess(Map<String, Object> config) {
                fillUiFromConfig(config);
            }

            @Override
            public void onFailure(String errorMsg) {
                ToastUtil.showCustomToast(SettingsActivity.this, errorMsg);
            }
        });
        updateApiEndpointViews();
        updatePythonLikeFieldsVisibility();
    }

    private void fillUiFromConfig(Map<String, Object> config) {
        DownloadStrategyType cur = configManager.getCurrentStrategy();
        if (cur == DownloadStrategyType.LEGACY) {
            etInstallId.setText((String) config.getOrDefault("installId", ""));
            etServerDeviceId.setText((String) config.getOrDefault("serverDeviceId", ""));
            etAid.setText((String) config.getOrDefault("aid", ""));
            etUpdateVersionCode.setText((String) config.getOrDefault("updateVersionCode", ""));
        } else {
            etServerUrl.setText((String) config.getOrDefault("server_url", ""));
            etAuthToken.setText((String) config.getOrDefault("auth_token", ""));
            etBatchSize.setText(String.valueOf(config.getOrDefault("max_batch_size", 10)));
            etBatchName.setText((String) config.getOrDefault("name", ""));

            // 加载JSON配置
            if (etJsonConfig != null) {
                etJsonConfig.setText((String) config.getOrDefault("json_config", ""));
            }
        }
    }

    private void saveSettingsByStrategy() {
        DownloadStrategyType strategy = rbPythonLike.isChecked() ? DownloadStrategyType.PYTHON_LIKE
                : DownloadStrategyType.LEGACY;
        Map<String, Object> values = new java.util.HashMap<>();
        if (strategy == DownloadStrategyType.LEGACY) {
            values.put("installId", etInstallId.getText().toString().trim());
            values.put("serverDeviceId", etServerDeviceId.getText().toString().trim());
            values.put("aid", etAid.getText().toString().trim());
            values.put("updateVersionCode", etUpdateVersionCode.getText().toString().trim());
        } else {
            values.put("server_url", etServerUrl.getText().toString().trim());
            values.put("auth_token", etAuthToken.getText().toString().trim());
            values.put("max_batch_size", etBatchSize.getText().toString().trim());
            values.put("name", etBatchName.getText().toString().trim());

            // 添加JSON配置
            if (etJsonConfig != null) {
                String jsonConfig = etJsonConfig.getText().toString().trim();
                values.put("json_config", jsonConfig);

                // 验证JSON配置（如果不为空）
                if (!jsonConfig.isEmpty()) {
                    JsonConfigParser.ParseResult result = JsonConfigParser.parseJsonConfig(jsonConfig);
                    if (!result.success) {
                        ToastUtil.showCustomToast(this, "JSON配置格式错误: " + result.errorMessage);
                        return;
                    }
                }
            }
        }
        currentStrategy.updateConfig(this, values, new ISettingsStrategy.Callback() {
            @Override
            public void onSuccess(Map<String, Object> config) {
                currentStrategy.onSuccess(SettingsActivity.this);
            }

            @Override
            public void onFailure(String errorMsg) {
                currentStrategy.onFailure(SettingsActivity.this, errorMsg);
            }
        });
    }

    private void updatePythonLikeFieldsVisibility() {
        boolean isPythonLike = configManager.getCurrentStrategy() == DownloadStrategyType.PYTHON_LIKE;
        if (groupLegacyConfig != null)
            groupLegacyConfig.setVisibility(isPythonLike ? View.GONE : View.VISIBLE);
        if (groupPythonLikeConfig != null)
            groupPythonLikeConfig.setVisibility(isPythonLike ? View.VISIBLE : View.GONE);
        if (tilBatchSize != null)
            tilBatchSize.setVisibility(isPythonLike ? View.VISIBLE : View.GONE);
    }

    private void updateApiEndpointViews() {
        // 新实现：组装分组数据，始终平铺所有分组和条目
        apiGroupDataList.clear();
        List<ApiGroupAdapter.ApiEntry> batchList = new ArrayList<>();
        List<ApiGroupAdapter.ApiEntry> singleList = new ArrayList<>();
        for (com.fanqie.novel.data.model.ApiEndpoint endpoint : configManager.getApiEndpoints()) {
            if (endpoint.getName() != null && endpoint.getName().contains("批量")) {
                batchList.add(new ApiGroupAdapter.ApiEntry(null, endpoint.getUrl()));
            } else {
                singleList.add(new ApiGroupAdapter.ApiEntry(endpoint.getName(), endpoint.getUrl()));
            }
        }
        ApiGroupAdapter.ApiGroup batchGroup = new ApiGroupAdapter.ApiGroup("批量下载API", batchList);
        ApiGroupAdapter.ApiGroup singleGroup = new ApiGroupAdapter.ApiGroup("单章下载API", singleList);
        apiGroupDataList.add(batchGroup);
        apiGroupDataList.addAll(batchGroup.entryList);
        apiGroupDataList.add(singleGroup);
        apiGroupDataList.addAll(singleGroup.entryList);
        apiGroupAdapter.notifyDataSetChanged();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 初始化JSON配置相关控件
     */
    private void initJsonConfigViews() {
        etJsonConfig = findViewById(R.id.et_json_config);
        btnValidateJson = findViewById(R.id.btn_validate_json);
        btnPreviewJson = findViewById(R.id.btn_preview_json);
        btnLoadJsonExample = findViewById(R.id.btn_load_json_example);
        btnTestJsonConfig = findViewById(R.id.btn_test_json_config);
        tvJsonStatus = findViewById(R.id.tv_json_status);

        // 验证JSON按钮点击事件
        btnValidateJson.setOnClickListener(v -> validateJsonConfig());

        // 预览配置按钮点击事件
        btnPreviewJson.setOnClickListener(v -> previewJsonConfig());

        // 加载示例配置按钮点击事件
        btnLoadJsonExample.setOnClickListener(v -> loadJsonExample());
    }

    /**
     * 验证JSON配置
     */
    private void validateJsonConfig() {
        String jsonText = etJsonConfig.getText().toString().trim();

        if (jsonText.isEmpty()) {
            showJsonStatus("请输入JSON配置", false);
            return;
        }

        JsonConfigParser.ParseResult result = JsonConfigParser.parseJsonConfig(jsonText);

        if (result.success) {
            showJsonStatus("✓ JSON格式正确，包含 " + result.apiEndpoints.size() + " 个API端点", true);
        } else {
            showJsonStatus("✗ " + result.errorMessage, false);
        }
    }

    /**
     * 预览JSON配置
     */
    private void previewJsonConfig() {
        String jsonText = etJsonConfig.getText().toString().trim();

        if (jsonText.isEmpty()) {
            ToastUtil.showCustomToast(this, "请先输入JSON配置");
            return;
        }

        JsonConfigParser.ParseResult result = JsonConfigParser.parseJsonConfig(jsonText);

        if (!result.success) {
            ToastUtil.showCustomToast(this, "JSON格式错误: " + result.errorMessage);
            return;
        }

        // 构建预览信息
        StringBuilder preview = new StringBuilder();
        preview.append("配置预览:\n\n");

        // 基础配置
        if (!result.basicConfig.isEmpty()) {
            preview.append("基础配置:\n");
            for (Map.Entry<String, Object> entry : result.basicConfig.entrySet()) {
                preview.append("  ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }
            preview.append("\n");
        }

        // API端点
        if (!result.apiEndpoints.isEmpty()) {
            preview.append("API端点 (").append(result.apiEndpoints.size()).append("个):\n");
            for (int i = 0; i < result.apiEndpoints.size(); i++) {
                var endpoint = result.apiEndpoints.get(i);
                preview.append("  ").append(i + 1).append(". ").append(endpoint.getName())
                        .append(": ").append(endpoint.getUrl()).append("\n");
            }
            preview.append("\n");
        }

        // 批量配置
        if (result.batchConfig != null) {
            preview.append("批量配置:\n");
            preview.append("  启用: ").append(result.batchConfig.isEnabled()).append("\n");
            preview.append("  批次大小: ").append(result.batchConfig.getMaxBatchSize()).append("\n");
            preview.append("  基础URL: ").append(result.batchConfig.getBaseUrl()).append("\n");
        }

        // 显示预览对话框
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("JSON配置预览")
                .setMessage(preview.toString())
                .setPositiveButton("确定", null)
                .show();
    }

    /**
     * 加载示例JSON配置
     */
    private void loadJsonExample() {
        String exampleJson = "{\n" +
                "  \"max_workers\": 4,\n" +
                "  \"max_retries\": 3,\n" +
                "  \"request_timeout\": 15,\n" +
                "  \"status_file\": \"chapter.json\",\n" +
                "  \"request_rate_limit\": 0.4,\n" +
                "  \"auth_token\": \"wcnmd91jb\",\n" +
                "  \"server_url\": \"https://dlbkltos.s7123.xyz:5080\",\n" +
                "  \"api_endpoints\": [\n" +
                "    {\n" +
                "      \"url\": \"https://qyuing.v2.sukimon.me:4380/batch_chapter?item_ids={chapter_id}\",\n" +
                "      \"name\": \"qyuing\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"url\": \"https://sdkapi.fanqieopen.com/open_sdk/reader/content/v1\",\n" +
                "      \"name\": \"fanqie_sdk\",\n" +
                "      \"params\": {\n" +
                "        \"novelsdk_aid\": \"638505\",\n" +
                "        \"sdk_type\": \"4\"\n" +
                "      },\n" +
                "      \"data\": {\n" +
                "        \"item_id\": \"{chapter_id}\",\n" +
                "        \"need_book_info\": 1,\n" +
                "        \"sdk_type\": 1,\n" +
                "        \"show_picture\": 1\n" +
                "      }\n" +
                "    }\n" +
                "  ],\n" +
                "  \"batch_config\": {\n" +
                "    \"name\": \"qyuing\",\n" +
                "    \"base_url\": \"https://qyuing.v2.sukimon.me:4380\",\n" +
                "    \"batch_endpoint\": \"/batch_chapter\",\n" +
                "    \"token\": \"34fc1a0a9b43\",\n" +
                "    \"max_batch_size\": 290,\n" +
                "    \"timeout\": 10,\n" +
                "    \"enabled\": true\n" +
                "  }\n" +
                "}";

        etJsonConfig.setText(exampleJson);
        showJsonStatus("已加载示例配置", true);
    }

    /**
     * 显示JSON状态信息
     */
    private void showJsonStatus(String message, boolean isSuccess) {
        tvJsonStatus.setText(message);
        tvJsonStatus.setTextColor(isSuccess ? getResources().getColor(android.R.color.holo_green_dark)
                : getResources().getColor(android.R.color.holo_red_dark));
        tvJsonStatus.setVisibility(View.VISIBLE);
    }
}