package com.fanqie.novel.ui.fragment;

import static com.fanqie.novel.constants.BookConstants.CHANNEL_ID;
import static com.fanqie.novel.constants.BookConstants.CHANNEL_NAME;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.fanqie.novel.R;
import com.fanqie.novel.constants.BookConstants;
import com.fanqie.novel.constants.CommonCache;
import com.fanqie.novel.data.db.mapper.BookMapper;
import com.fanqie.novel.data.db.mapper.ChapterMapper;
import com.fanqie.novel.data.db.mapper.TomatoVariableMapper;
import com.fanqie.novel.data.model.BookInfo;
import com.fanqie.novel.data.model.TomatoVariable;
import com.fanqie.novel.service.NovelDownloadService;
import com.fanqie.novel.strategy.ChapterDownloadStrategy;
import com.fanqie.novel.strategy.impl.down.OpenSourceDownloadStrategy;
import com.fanqie.novel.strategy.impl.down.TomatoDownloadStrategy;
import com.fanqie.novel.ui.activity.BookDetailActivity;
import com.fanqie.novel.ui.adapter.BookshelfAdapter;
import com.fanqie.novel.util.BookDataExportUtil;
import com.fanqie.novel.util.DownUtil;
import com.fanqie.novel.util.DownloadConfigManager;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

public class BookshelfFragment extends Fragment implements BookshelfAdapter.BookItemClickListener {
    private static final String TAG = "BookshelfFragment";
    public static final String ACTION_DOWNLOAD_PROGRESS = "com.fanqie.novel.DOWNLOAD_PROGRESS";
    public static final String ACTION_DOWNLOAD_COMPLETE = "com.fanqie.novel.DOWNLOAD_COMPLETE";
    public static final String ACTION_REFRESH_BOOKSHELF = "com.fanqie.novel.REFRESH_BOOKSHELF";
    
    private RecyclerView recyclerView;
    private BookshelfAdapter adapter;
    private List<BookInfo> bookList = new ArrayList<>();
    private SwipeRefreshLayout swipeRefreshLayout;
    private TextView emptyView;
    private BookMapper bookMapper;
    private ChapterMapper chapterMapper;
    private TomatoVariableMapper tomatoVariableMapper;
    
    // 通知管理器
    private NotificationManager notificationManager;
    // 主线程Handler
    private Handler mainHandler;

    // 添加文件保存结果的处理器
    private ActivityResultLauncher<Intent> saveFileLauncher;
    // 标记是否需要刷新列表
    private boolean needRefresh = false;
    
    // 下载进度广播接收器
    private BroadcastReceiver downloadProgressReceiver;

    public BookshelfFragment() {
        // 必需的空构造函数
    }

    public static BookshelfFragment newInstance() {
        return new BookshelfFragment();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.fragment_bookshelf, container, false);

        // 初始化视图
        recyclerView = rootView.findViewById(R.id.recycler_view);
        swipeRefreshLayout = rootView.findViewById(R.id.swipe_refresh_layout);
        emptyView = rootView.findViewById(R.id.empty_view);

        return rootView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 初始化数据库
        bookMapper = BookMapper.getInstance(requireContext());
        chapterMapper = ChapterMapper.getInstance(requireContext());
        tomatoVariableMapper = TomatoVariableMapper.getInstance(requireContext());
        
        // 初始化通知管理器
        notificationManager = (NotificationManager) requireContext().getSystemService(Context.NOTIFICATION_SERVICE);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_LOW);
            notificationManager.createNotificationChannel(channel);
        }
        
        // 初始化主线程Handler
        mainHandler = new Handler(Looper.getMainLooper());
        
        // 初始化TomatoRequest
        initTomatoRequest();

        // 设置RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        adapter = new BookshelfAdapter(requireContext(), bookList, this);
        recyclerView.setAdapter(adapter);

        // 设置下拉刷新
        swipeRefreshLayout.setOnRefreshListener(this::refreshBookList);

        // 注册文件保存结果处理器
        saveFileLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> BookDataExportUtil.handleSaveResult(result, requireContext().getContentResolver(), requireContext())
        );
        
        // 注册下载进度广播接收器
        registerDownloadProgressReceiver();
        registerRefreshBookshelfReceiver();

        // 加载书籍列表
        loadBookList();
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        // 注销广播接收器
        if (downloadProgressReceiver != null) {
            LocalBroadcastManager.getInstance(requireContext())
                .unregisterReceiver(downloadProgressReceiver);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 只有在标记需要刷新时才刷新列表
        if (needRefresh) {
            refreshBookList();
            needRefresh = false;
        }
    }

    private void loadBookList() {
        swipeRefreshLayout.setRefreshing(true);

        // 从数据库加载书籍列表
        bookList.clear();
        bookList.addAll(bookMapper.getBookList(null));

        // 为每本书设置章节计数
        for (BookInfo bookInfo : bookList) {
            String countChapters = chapterMapper.countChapters(bookInfo.getBookId());
            bookInfo.setChapterCount(countChapters);
        }

        // 更新UI
        updateUI();

        // 异步加载最新章节信息
        updateLatestChapterInfo();
    }

    private void refreshBookList() {
        loadBookList();
    }

    private void updateUI() {
        adapter.notifyDataSetChanged();
        swipeRefreshLayout.setRefreshing(false);

        // 显示/隐藏空视图
        if (bookList.isEmpty()) {
            emptyView.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.GONE);
        } else {
            emptyView.setVisibility(View.GONE);
            recyclerView.setVisibility(View.VISIBLE);
        }
    }

    private void updateLatestChapterInfo() {
        // 异步更新最新章节信息
        CompletableFuture.runAsync(() -> {
            List<BookInfo> updatedBooks = new ArrayList<>();
            ChapterDownloadStrategy strategy;
            DownloadConfigManager configManager = DownloadConfigManager.getInstance(requireContext());
            switch (configManager.getCurrentStrategy()) {
                case PYTHON_LIKE:
                    strategy = new OpenSourceDownloadStrategy();
                    break;
                case LEGACY:
                default:
                    strategy = new TomatoDownloadStrategy();
                    break;
            }
            for (BookInfo bookInfo : bookList) {
                try {
                    com.fanqie.novel.util.LogUtil.d(TAG, "异步更新书籍信息: " + bookInfo.getBookId());
                    BookInfo cached = configManager.getBookInfoById(bookInfo.getBookId());
                    if (cached != null && cached.getLastUpdateChapter() != null) {
                        bookInfo.setLastUpdateChapter(cached.getLastUpdateChapter());
                        updatedBooks.add(bookInfo);
                        continue;
                    }
                    // 策略异步拉取
                    CountDownLatch latch = new CountDownLatch(1);
                    strategy.getBookInfo(bookInfo.getBookId(), new ChapterDownloadStrategy.BookInfoCallback() {
                        @Override
                        public void onSuccess(BookInfo info) {
                            configManager.cacheBookInfo(info);
                            bookInfo.setLastUpdateChapter(info.getLastUpdateChapter());
                            updatedBooks.add(bookInfo);
                            latch.countDown();
                        }
                        @Override
                        public void onFailure(Throwable error) {
                            latch.countDown();
                        }
                    });
                    latch.await(10, java.util.concurrent.TimeUnit.SECONDS);
                } catch (Exception e) {
                    com.fanqie.novel.util.LogUtil.e(TAG, "更新书籍信息失败: " + bookInfo.getBookId(), e);
                }
            }
            // 只有在有更新的书籍时才刷新UI，且只刷新一次
            if (!updatedBooks.isEmpty() && getActivity() != null) {
                getActivity().runOnUiThread(() -> adapter.notifyDataSetChanged());
            }
        });
    }

    // 实现接口方法
    @Override
    public void onBookItemClick(BookInfo book) {
        // 打开书籍详情页
        Intent intent = new Intent(requireContext(), BookDetailActivity.class);
        intent.putExtra("bookId", book.getBookId());
        intent.putExtra("imgUrl", book.getImageUrl());
        intent.putExtra("bookName", book.getBookName());
        intent.putExtra("pageName", BookConstants.PAGE_NAME_BOOK_INFO);
        startActivity(intent);
    }

    @Override
    public void onDownloadClick(BookInfo book) {
        DownloadConfigManager configManager = DownloadConfigManager.getInstance(requireContext());
        configManager.cacheBookInfo(book);
        Intent intent = new Intent(requireContext(), NovelDownloadService.class);
        intent.putExtra("bookId", book.getBookId());
        CommonCache.putBookName(book.getBookId(), book.getBookName());
        requireContext().startService(intent);
    }

    @Override
    public void onExportClick(BookInfo book) {
        // 使用BookExportDownloadUtil工具类处理导出功能
        com.fanqie.novel.util.BookExportDownloadUtil.exportBook(
                requireContext(),
                book,
                saveFileLauncher,
                swipeRefreshLayout,
                new com.fanqie.novel.util.BookExportDownloadUtil.ExportCallback() {
                    @Override
                    public void onExportSuccess(BookInfo updatedBook) {
                        // 只更新这一本书的UI
                        int position = bookList.indexOf(updatedBook);
                        if (position != -1) {
                            updatedBook.setStatus(2); // 更新本地对象状态
                            adapter.notifyItemChanged(position);
                        }
                    }

                    @Override
                    public void onExportFailed(BookInfo book, String errorMsg) {
                        // 错误处理已在工具类中完成
                    }
                }
        );
    }

    @Override
    public void onDeleteClick(BookInfo book) {
        // 删除书籍
        bookMapper.deleteBookByBookId(book.getBookId());
        chapterMapper.deleteChaptersByBookId(book.getBookId());

        // 从当前列表中移除并更新UI，而不是刷新整个列表
        int position = bookList.indexOf(book);
        if (position != -1) {
            bookList.remove(position);
            adapter.notifyItemRemoved(position);
            
            // 更新空视图状态
            if (bookList.isEmpty()) {
                emptyView.setVisibility(View.VISIBLE);
                recyclerView.setVisibility(View.GONE);
            }
        }
        
        // 使用showSuccessToast方法
        com.fanqie.novel.util.ToastUtil.showSuccessToast(requireContext(), "已删除：" + book.getBookName());
    }
    
    /**
     * 显示下载进度通知
     */
    private void showDownloadNotification(String bookId, String bookName, int progress, int processedChapters, int totalChapters) {
        try {
            NotificationCompat.Builder builder = new NotificationCompat.Builder(requireContext(), CHANNEL_ID)
                    .setSmallIcon(android.R.drawable.stat_sys_download)
                    .setContentTitle(bookName)
                    .setContentText(progress + "% " + processedChapters + "/" + totalChapters)
                    .setProgress(totalChapters, processedChapters, false)
                    .setOngoing(true)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setAutoCancel(false);

            // 确保在主线程中显示通知
            mainHandler.post(() -> {
                try {
                    notificationManager.notify(CommonCache.getBookNotificationId(bookId), builder.build());
                } catch (Exception e) {
                    com.fanqie.novel.util.LogUtil.e(TAG, "显示下载通知失败", e);
                }
            });
        } catch (Exception e) {
            com.fanqie.novel.util.LogUtil.e(TAG, "创建下载通知失败", e);
        }
    }
    
    /**
     * 显示下载完成通知
     */
    private void showDownloadCompleteNotification(String bookId, String bookName) {
        try {
            com.fanqie.novel.util.LogUtil.d(TAG, "显示下载完成通知: " + bookName);
            NotificationCompat.Builder builder = new NotificationCompat.Builder(requireContext(), CHANNEL_ID)
                    .setSmallIcon(android.R.drawable.stat_sys_download_done)
                    .setContentTitle(bookName)
                    .setContentText("下载完成")
                    .setProgress(0, 0, false)
                    .setOngoing(false)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setAutoCancel(true);

            // 确保在主线程中显示通知
            mainHandler.post(() -> {
                try {
                    notificationManager.notify(CommonCache.getBookNotificationId(bookId), builder.build());
                    com.fanqie.novel.util.LogUtil.d(TAG, "下载完成通知显示成功: bookId=" + bookId);
                    
                    // 延迟移除通知
                    mainHandler.postDelayed(() -> notificationManager.cancel(CommonCache.getBookNotificationId(bookId)), 3000);
                } catch (Exception e) {
                    com.fanqie.novel.util.LogUtil.e(TAG, "显示下载完成通知失败", e);
                }
            });
        } catch (Exception e) {
            com.fanqie.novel.util.LogUtil.e(TAG, "创建下载完成通知失败", e);
        }
    }
    
    /**
     * 注册下载进度广播接收器
     */
    private void registerDownloadProgressReceiver() {
        downloadProgressReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (action == null) return;
                
                String bookId = intent.getStringExtra("bookId");
                if (bookId == null) return;
                
                if (ACTION_DOWNLOAD_PROGRESS.equals(action)) {
                    int progress = intent.getIntExtra("progress", 0);
                    int processedChapters = intent.getIntExtra("processedChapters", 0);
                    int totalChapters = intent.getIntExtra("totalChapters", 0);
                    
                    // 更新通知进度
                    showDownloadNotification(bookId, CommonCache.getBookName(bookId), 
                            progress, processedChapters, totalChapters);
                    
                    // 更新列表项进度显示
                    updateBookProgressInList(bookId, progress, processedChapters, totalChapters);
                    
                } else if (ACTION_DOWNLOAD_COMPLETE.equals(action)) {
                    // 显示下载完成通知
                    showDownloadCompleteNotification(bookId, CommonCache.getBookName(bookId));
                    
                    // 标记列表项下载完成
                    finishBookDownloadInList(bookId);
                }
            }
        };
        
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_DOWNLOAD_PROGRESS);
        filter.addAction(ACTION_DOWNLOAD_COMPLETE);
        
        LocalBroadcastManager.getInstance(requireContext())
            .registerReceiver(downloadProgressReceiver, filter);
    }
    
    /**
     * 更新列表中书籍的下载进度
     */
    private void updateBookProgressInList(String bookId, int progress, int processedChapters, int totalChapters) {
        if (bookList == null || bookList.isEmpty()) return;
        
        // 找到对应的书籍并更新
        for (int i = 0; i < bookList.size(); i++) {
            BookInfo book = bookList.get(i);
            if (book.getBookId().equals(bookId)) {
                // 确保下载状态为true
                book.setDownloading(true);
                
                // 更新下载进度
                boolean completed = book.updateDownloadProgress(processedChapters, totalChapters);
                
                // 使用payload进行局部更新，避免图片闪烁
                if (adapter != null) {
                    adapter.notifyItemChanged(i, "PROGRESS_UPDATE");
                }
                
                break;
            }
        }
    }
    
    /**
     * 标记列表中书籍的下载已完成
     */
    private void finishBookDownloadInList(String bookId) {
        if (bookList == null || bookList.isEmpty()) return;
        
        // 找到对应的书籍并更新
        for (int i = 0; i < bookList.size(); i++) {
            BookInfo book = bookList.get(i);
            if (book.getBookId().equals(bookId)) {
                // 设置下载状态为false
                book.setDownloading(false);
                // 设置状态为已下载
                book.setStatus(1);
                book.setStatusZh("下载完成");
                
                // 使用payload进行局部更新，避免图片闪烁
                if (adapter != null) {
                    adapter.notifyItemChanged(i, "PROGRESS_UPDATE");
                }
                
                break;
            }
        }
    }
    
    /**
     * 发送下载进度广播（供下载服务调用）
     */
    public static void sendDownloadProgressBroadcast(Context context, String bookId, 
                                                     int progress, int processedChapters, int totalChapters) {
        Intent intent = new Intent(ACTION_DOWNLOAD_PROGRESS);
        intent.putExtra("bookId", bookId);
        intent.putExtra("progress", progress);
        intent.putExtra("processedChapters", processedChapters);
        intent.putExtra("totalChapters", totalChapters);
        
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }
    
    /**
     * 发送下载完成广播（供下载服务调用）
     */
    public static void sendDownloadCompleteBroadcast(Context context, String bookId) {
        Intent intent = new Intent(ACTION_DOWNLOAD_COMPLETE);
        intent.putExtra("bookId", bookId);
        
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    private void initTomatoRequest() {
        try {
            com.fanqie.novel.util.LogUtil.d(TAG, "初始化TomatoRequest");
            
            // 获取TomatoVariable
            TomatoVariable tomatoVariable = tomatoVariableMapper.queryTomatoVariable();
            
            // 如果没有配置，创建一个默认配置
            if (tomatoVariable == null) {
                com.fanqie.novel.util.LogUtil.d(TAG, "未找到TomatoVariable配置，创建默认配置");
                tomatoVariable = new TomatoVariable("4427064614339001", "4427064614334905", "1967", "62532");
                tomatoVariableMapper.insertTomatoVariable(
                    "4427064614339001", 
                    "4427064614334905", 
                    "1967", 
                    "62532");
            }
            
            // 创建TomatoRequest实例并设置到DownUtil
            com.fanqie.novel.util.TomatoRequest tomatoRequest = new com.fanqie.novel.util.TomatoRequest(tomatoVariable);
            DownUtil.setTomatoRequest(tomatoRequest);
            
            com.fanqie.novel.util.LogUtil.d(TAG, "TomatoRequest初始化成功");
        } catch (Exception e) {
            com.fanqie.novel.util.LogUtil.e(TAG, "初始化TomatoRequest失败", e);
        }
    }

    private void registerRefreshBookshelfReceiver() {
        BroadcastReceiver refreshBookshelfReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (ACTION_REFRESH_BOOKSHELF.equals(action)) {
                    loadBookList();
                }
            }
        };
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_REFRESH_BOOKSHELF);
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(refreshBookshelfReceiver, filter);
    }

    public static void sendRefreshBookshelfBroadcast(Context context) {
        Intent intent = new Intent(ACTION_REFRESH_BOOKSHELF);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }
} 