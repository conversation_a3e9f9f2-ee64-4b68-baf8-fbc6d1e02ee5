package com.fanqie.novel.data.db.mapper;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import com.fanqie.novel.util.LogUtil;

import java.util.Locale;

public class VipMapper extends BaseMapper {
    private static final String TAG = "VipMapper";
    private static final String TABLE_SVIP_COOKIE = "svip_cookie";
    private static final String COLUMN_SVIP_COOKIE = "svip_cookie";
    private static volatile VipMapper instance;

    private VipMapper(Context context) {
        super(context);
    }

    public static VipMapper getInstance(Context context) {
        if (instance == null) {
            synchronized (VipMapper.class) {
                if (instance == null) {
                    instance = new VipMapper(context.getApplicationContext());
                }
            }
        }
        return instance;
    }

    public String getSvipCookie() {
        LogUtil.d(TAG, "获取SVIP Cookie");
        String cookie = null;
        try (Cursor cursor = db.query(TABLE_SVIP_COOKIE,
                new String[]{COLUMN_SVIP_COOKIE},
                null, null, null, null, null)) {
            if (cursor.moveToFirst()) {
                cookie = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_SVIP_COOKIE));
                LogUtil.i(TAG, String.format(Locale.getDefault(), "成功获取SVIP Cookie"));
            } else {
                LogUtil.w(TAG, "未找到SVIP Cookie");
            }
        }
        return cookie;
    }

    public void saveSvipCookie(String cookie) {
        LogUtil.d(TAG, "保存SVIP Cookie");
        db.delete(TABLE_SVIP_COOKIE, null, null);
        ContentValues values = new ContentValues();
        values.put(COLUMN_SVIP_COOKIE, cookie);
        long result = db.insert(TABLE_SVIP_COOKIE, null, values);
        if (result != -1) {
            LogUtil.i(TAG, "SVIP Cookie保存成功");
        } else {
            LogUtil.e(TAG, "SVIP Cookie保存失败");
        }
    }
} 