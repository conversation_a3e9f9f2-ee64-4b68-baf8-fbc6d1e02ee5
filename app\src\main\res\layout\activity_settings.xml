<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/purple_66b"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="下载方式选择"
                android:textSize="16sp"
                android:textStyle="bold" />

            <RadioGroup
                android:id="@+id/rg_download_strategy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">
                <RadioButton
                    android:id="@+id/rb_legacy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="官方API模式" />
                <RadioButton
                    android:id="@+id/rb_python_like"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="开源API模式" />
            </RadioGroup>

            <!-- 默认模式配置分组 -->
            <LinearLayout
                android:id="@+id/group_legacy_config"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_install_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_install_id"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="安装ID (Install ID)"
                        android:inputType="text" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_server_device_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_server_device_id"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="服务器设备ID (Server Device ID)"
                        android:inputType="text" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_aid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_aid"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="辅助ID (Aid)"
                        android:inputType="text" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_update_version_code"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_update_version_code"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="更新版本代码 (Update Version Code)"
                        android:inputType="text" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <!-- Python模式配置分组 -->
            <LinearLayout
                android:id="@+id/group_python_like_config"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_server_url"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_server_url"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="服务器地址 (server_url)"
                        android:inputType="textUri" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_auth_token"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_auth_token"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="认证Token (auth_token)"
                        android:inputType="text" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_batch_size"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_batch_size"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="批量下载每批个数 (batch_size)"
                        android:inputType="number" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_batch_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_batch_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="批量API名称 (name)"
                        android:inputType="text" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- JSON配置分割线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:background="#E0E0E0" />

                <!-- JSON配置标题 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="JSON配置模式"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="可直接输入JSON配置作为备用方案，当URL获取失败时自动使用"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:layout_marginBottom="12dp" />

                <!-- JSON配置输入框 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_json_config"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_json_config"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="JSON配置内容"
                        android:inputType="textMultiLine"
                        android:minLines="8"
                        android:maxLines="15"
                        android:scrollbars="vertical"
                        android:gravity="top|start" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- JSON配置操作按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <Button
                        android:id="@+id/btn_validate_json"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="验证JSON"
                        android:backgroundTint="#4CAF50"
                        style="@style/Widget.MaterialComponents.Button" />

                    <Button
                        android:id="@+id/btn_preview_json"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="预览配置"
                        android:backgroundTint="#FF9800"
                        style="@style/Widget.MaterialComponents.Button" />
                </LinearLayout>

                <!-- JSON配置状态显示 -->
                <TextView
                    android:id="@+id/tv_json_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:textSize="12sp"
                    android:visibility="gone" />

                <!-- JSON配置示例按钮 -->
                <Button
                    android:id="@+id/btn_load_json_example"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="加载示例配置"
                    android:backgroundTint="#9C27B0"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                <!-- JSON配置测试按钮 -->
                <Button
                    android:id="@+id/btn_test_json_config"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="测试JSON配置功能"
                    android:backgroundTint="#607D8B"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_api_group"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp" />
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="注意：修改这些设置可能会影响应用的正常功能，请谨慎操作。"
                android:textColor="#FF5722"
                android:textSize="14sp" />

            <Button
                android:backgroundTint="#1890FF"
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="保存设置" />

        </LinearLayout>
    </ScrollView>
</LinearLayout> 