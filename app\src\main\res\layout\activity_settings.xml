<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/purple_66b"
            android:elevation="4dp"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"/>

    <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

            <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="下载方式选择"
                    android:textSize="16sp"
                    android:textStyle="bold"/>

            <RadioGroup
                android:id="@+id/rg_download_strategy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">
                <RadioButton
                    android:id="@+id/rb_legacy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="官方API模式"/>
                <RadioButton
                    android:id="@+id/rb_python_like"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="开源API模式"/>
            </RadioGroup>

            <!-- 默认模式配置分组 -->
            <LinearLayout
                android:id="@+id/group_legacy_config"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_install_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_install_id"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="安装ID (Install ID)"
                        android:inputType="text"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_server_device_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_server_device_id"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="服务器设备ID (Server Device ID)"
                        android:inputType="text"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_aid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_aid"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="辅助ID (Aid)"
                        android:inputType="text"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_update_version_code"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_update_version_code"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="更新版本代码 (Update Version Code)"
                        android:inputType="text"/>
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <!-- Python模式配置分组 -->
            <LinearLayout
                android:id="@+id/group_python_like_config"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_server_url"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_server_url"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="服务器地址 (server_url)"
                        android:inputType="textUri"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_auth_token"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_auth_token"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="认证Token (auth_token)"
                        android:inputType="text"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_batch_size"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_batch_size"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="批量下载每批个数 (batch_size)"
                        android:inputType="number"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_batch_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_batch_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="批量API名称 (name)"
                        android:inputType="text"/>
                </com.google.android.material.textfield.TextInputLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_api_group"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"/>
            </LinearLayout>

            <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="注意：修改这些设置可能会影响应用的正常功能，请谨慎操作。"
                    android:textColor="#FF5722"
                    android:textSize="14sp"/>

            <Button
                    android:backgroundTint="#1890FF"
                    android:id="@+id/btn_save"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="保存设置"/>

        </LinearLayout>
    </ScrollView>
</LinearLayout> 