<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                   xmlns:app="http://schemas.android.com/apk/res-auto"
                                                   xmlns:tools="http://schemas.android.com/tools"
                                                   android:layout_width="match_parent"
                                                   android:layout_height="match_parent"
                                                   android:background="#F5F5F5"
                                                   tools:context=".fragments.SearchFragment">

    <androidx.cardview.widget.CardView
            android:id="@+id/search_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="7dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:cardUseCompatPadding="true"
            android:transitionName="search_card_transition"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
                android:id="@+id/search_input_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="8dp">

            <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                <EditText
                        android:id="@+id/search_edit_text"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        android:background="@drawable/search_input_background_transition"
                        android:hint="请输入搜索内容"
                        android:imeOptions="actionSearch"
                        android:inputType="text"
                        android:paddingStart="16dp"
                        android:paddingEnd="40dp"
                        android:textColor="#333333"
                        android:textColorHint="#AAAAAA"
                        android:textSize="16sp"/>

                <ImageButton
                        android:id="@+id/clear_button"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_gravity="end|center_vertical"
                        android:layout_marginEnd="8dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="清除"
                        android:src="@drawable/ic_clear"
                        android:visibility="gone"/>
            </FrameLayout>

            <TextView
                    android:id="@+id/search_button"
                    android:layout_width="wrap_content"
                    android:layout_height="44dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/search_button_background"
                    android:gravity="center"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="搜索"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/search_results_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="12dp"
            android:paddingBottom="8dp"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/search_card"
            tools:listitem="@layout/item_search_result"/>

    <!-- 友好提示区 -->
    <TextView
        android:id="@+id/search_hint_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="请输入搜索内容"
        android:textColor="#AAAAAA"
        android:textSize="16sp"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/search_card"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ProgressBar
            android:id="@+id/search_progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/search_card"/>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/back_to_top_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:contentDescription="回到顶部"
            android:src="@drawable/ic_arrow_up"
            android:visibility="gone"
            app:backgroundTint="@color/purple_66b"
            app:fabSize="mini"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout> 