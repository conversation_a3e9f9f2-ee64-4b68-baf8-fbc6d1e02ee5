package com.fanqie.novel.util;

import android.text.TextUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * HTML处理工具类
 */
public class HtmlUtils {

    private static final String TAG = "HtmlUtils";

    /**
     * 清理HTML标签
     * @param html 包含HTML标签的文本
     * @return 清理后的纯文本
     */
    public static String cleanHtml(String html) {
        if (TextUtils.isEmpty(html)) {
            return "";
        }

        try {
            // 处理常见的段落标签，保证换行
            String text = ContentCleaner.processContent(html);
            
            // 去除其他HTML标签
            text = text.replaceAll("<[^>]+>", "");
            
            // 处理HTML实体
            text = text.replaceAll("&nbsp;", " ")
                    .replaceAll("&lt;", "<")
                    .replaceAll("&gt;", ">")
                    .replaceAll("&amp;", "&")
                    .replaceAll("&quot;", "\"")
                    .replaceAll("&apos;", "'")
                    .replaceAll("&#160;", " ")
                    .replaceAll("&#8226;", "•")
                    .replaceAll("&#8212;", "—")
                    .replaceAll("&#8230;", "…");
            
            // 处理特殊情况的段落和空白
            text = normalizeWhitespace(text);
            
            // 处理常见的网页特殊符号
            text = handleSpecialCharacters(text);
            
            return text;
        } catch (Exception e) {
            LogUtil.e(TAG, "清理HTML标签失败: " + e.getMessage());
            return html; // 出错时返回原文本
        }
    }
    
    /**
     * 规范化空白字符
     */
    private static String normalizeWhitespace(String text) {
        // 替换连续空格为单个空格
        text = text.replaceAll("[ \\t]+", " ");
        
        // 替换连续超过两个的换行符为两个换行符（保留段落）
        text = text.replaceAll("\\n{3,}", "\n\n");
        
        // 删除行首行尾的空白
        text = text.replaceAll("(?m)^[ \\t]+|[ \\t]+$", "");
        
        // 确保段落之间有合适的间隔
        text = text.replaceAll("\n\n", "\n\n");
        
        return text.trim();
    }
    
    /**
     * 处理特殊字符
     */
    private static String handleSpecialCharacters(String text) {
        // 匹配Unicode表情符号和特殊字符
        Pattern unicodePattern = Pattern.compile("[\\x{10000}-\\x{10FFFF}]", 
                Pattern.UNICODE_CASE);
        Matcher matcher = unicodePattern.matcher(text);
        text = matcher.replaceAll("");
        
        // 匹配 URL 链接并删除
        text = text.replaceAll("https?://[\\w\\./\\-\\?=&%]+", "");
        
        // 移除广告和标记文本
        text = removeCommonAds(text);
        
        return text;
    }
    
    /**
     * 移除常见广告和标记文本
     */
    private static String removeCommonAds(String text) {
        // 常见广告标记
        String[] adMarkers = {
            "本章未完，请点击下一页继续阅读",
            "请收藏本站：",
            "加入书签",
            "\\d+个书迷长期追读",
            "请使用访问本站",
            "推荐阅读[：:].*",
            "手机阅读.*",
            "纯文学.*",
            "https?://.*",
            ".*最新章节.*",
            ".*txt下载.*",
            ".*手机版阅读.*",
            ".*章节目录.*",
            "\\(\\d+/\\d+\\)",
            "小说网",
            "免费阅读",
            "字节跳动",
            "点击下一页",
            "章节目录",
            "温馨提示：",
            "内容由.*整理",
            ".*欢迎阅读",
            ".*更多章节",
            ".*精彩继续"
        };
        
        for (String marker : adMarkers) {
            text = text.replaceAll(marker, "");
        }
        
        return text;
    }
    
    /**
     * 切分章节内容为多个页面
     * @param content 完整章节内容
     * @param charsPerPage 每页字符数
     * @return 分页后的内容数组
     */
    public static String[] paginateContent(String content, int charsPerPage) {
        if (TextUtils.isEmpty(content) || charsPerPage <= 0) {
            return new String[]{content};
        }
        
        // 按段落拆分
        String[] paragraphs = content.split("\n\n");
        
        // 存储所有页
        java.util.List<String> pages = new java.util.ArrayList<>();
        StringBuilder currentPage = new StringBuilder();
        int currentPageSize = 0;
        
        for (String paragraph : paragraphs) {
            paragraph = paragraph.trim();
            if (paragraph.isEmpty()) continue;
            
            // 如果段落很短，直接添加
            if (paragraph.length() + currentPageSize <= charsPerPage) {
                if (currentPageSize > 0) {
                    currentPage.append("\n\n");
                }
                currentPage.append(paragraph);
                currentPageSize += paragraph.length() + 2; // 加上换行符长度
            } else if (paragraph.length() > charsPerPage) {
                // 如果段落比整页还长，需要拆分段落
                
                // 先将当前页添加到结果中（如果有内容）
                if (currentPageSize > 0) {
                    pages.add(currentPage.toString());
                    currentPage = new StringBuilder();
                    currentPageSize = 0;
                }
                
                // 拆分长段落
                for (int i = 0; i < paragraph.length(); i += charsPerPage) {
                    int end = Math.min(i + charsPerPage, paragraph.length());
                    String part = paragraph.substring(i, end);
                    pages.add(part);
                }
            } else {
                // 当前段落加上会超出页面，先保存当前页面，再开始新页面
                pages.add(currentPage.toString());
                currentPage = new StringBuilder(paragraph);
                currentPageSize = paragraph.length();
            }
            
            // 检查当前页是否已满
            if (currentPageSize >= charsPerPage) {
                pages.add(currentPage.toString());
                currentPage = new StringBuilder();
                currentPageSize = 0;
            }
        }
        
        // 添加最后一页（如果有内容）
        if (currentPageSize > 0) {
            pages.add(currentPage.toString());
        }
        
        return pages.toArray(new String[0]);
    }
} 