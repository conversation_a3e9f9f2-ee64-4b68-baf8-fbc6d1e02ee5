package com.fanqie.novel.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.fanqie.novel.R;
import com.fanqie.novel.data.model.ChapterInfo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ChapterAdapter extends RecyclerView.Adapter<ChapterAdapter.ChapterViewHolder> {

    private List<ChapterInfo> chapterList;
    private OnChapterClickListener listener;

    public ChapterAdapter() {
        this.chapterList = new ArrayList<>();
    }

    public interface OnChapterClickListener {
        void onChapterClick(ChapterInfo chapter, int position);
    }

    public void setOnChapterClickListener(OnChapterClickListener listener) {
        this.listener = listener;
    }

    public void setChapters(List<ChapterInfo> chapters) {
        this.chapterList = chapters;
        notifyDataSetChanged();
    }

    public void addChapters(List<ChapterInfo> chapters) {
        int startPos = this.chapterList.size();
        this.chapterList.addAll(chapters);
        notifyItemRangeInserted(startPos, chapters.size());
    }

    public void clearChapters() {
        this.chapterList.clear();
        notifyDataSetChanged();
    }

    public void reverseChapters() {
        // 直接在现有列表上应用反转操作，效率更高
        Collections.reverse(chapterList);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ChapterViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_chapter, parent, false);
        return new ChapterViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ChapterViewHolder holder, int position) {
        ChapterInfo chapter = chapterList.get(position);
        holder.tvChapterTitle.setText(chapter.getTitle());
        
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onChapterClick(chapter, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return chapterList.size();
    }

    static class ChapterViewHolder extends RecyclerView.ViewHolder {
        TextView tvChapterTitle;

        ChapterViewHolder(@NonNull View itemView) {
            super(itemView);
            tvChapterTitle = itemView.findViewById(R.id.tv_chapter_title);
        }
    }
} 