package com.fanqie.novel.util;

import com.fanqie.novel.data.model.ApiEndpoint;
import com.fanqie.novel.data.model.ApiStatus;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * API状态管理工具类
 * 负责管理API端点的状态跟踪、性能排序和报告生成
 */
public class ApiStatusManager {

    // API状态跟踪 - 线程安全的静态变量，模拟Python中的函数属性
    private static final Map<String, ApiStatus> apiStatusMap = new ConcurrentHashMap<>();

    /**
     * 初始化API状态跟踪
     * @param endpoints API端点列表
     */
    public static void initializeApiStatus(List<ApiEndpoint> endpoints) {
        for (ApiEndpoint endpoint : endpoints) {
            String url = endpoint.getUrl();
            if (!apiStatusMap.containsKey(url)) {
                apiStatusMap.put(url, new ApiStatus());
            }
        }
    }

    /**
     * 根据历史性能对API端点进行智能排序
     * @param endpoints 原始API端点列表
     * @return 按性能排序后的API端点列表
     */
    public static List<ApiEndpoint> getSortedEndpointsByPerformance(List<ApiEndpoint> endpoints) {
        return endpoints.stream()
                .sorted(Comparator.comparingDouble(endpoint -> {
                    ApiStatus status = apiStatusMap.get(endpoint.getUrl());
                    return status != null ? status.getPriorityScore() : Double.MAX_VALUE;
                }))
                .collect(Collectors.toList());
    }

    /**
     * 获取指定URL的API状态
     * @param url API端点URL
     * @return API状态对象，如果不存在则返回null
     */
    public static ApiStatus getApiStatus(String url) {
        return apiStatusMap.get(url);
    }

    /**
     * 获取API状态统计信息（用于调试和监控）
     * @return 格式化的API状态报告字符串
     */
    public static String getApiStatusReport() {
        StringBuilder report = new StringBuilder("API状态报告:\n");
        for (Map.Entry<String, ApiStatus> entry : apiStatusMap.entrySet()) {
            String url = entry.getKey();
            ApiStatus status = entry.getValue();

            // 格式化响应时间显示
            String avgResponseTimeStr = ApiRequestUtil.formatResponseTime(status.getAverageResponseTime());
            String lastResponseTimeStr = ApiRequestUtil.formatResponseTime(status.getLastResponseTime());

            report.append(String.format("URL: %s\n", url))
                    .append(String.format("  成功次数: %d\n", status.getSuccessCount()))
                    .append(String.format("  错误次数: %d\n", status.getErrorCount()))
                    .append(String.format("  平均响应时间: %s\n", avgResponseTimeStr))
                    .append(String.format("  最后响应时间: %s\n", lastResponseTimeStr))
                    .append(String.format("  优先级分数: %.2f\n", status.getPriorityScore()))
                    .append("---\n");
        }
        return report.toString();
    }

    /**
     * 清空所有API状态数据
     * 主要用于测试或重置场景
     */
    public static void clearAllStatus() {
        apiStatusMap.clear();
    }

    /**
     * 获取API状态映射的大小
     * @return 当前跟踪的API端点数量
     */
    public static int getStatusMapSize() {
        return apiStatusMap.size();
    }

    /**
     * 测试响应时间格式化功能
     * @return 测试结果字符串
     */
    public static String testResponseTimeFormatting() {
        StringBuilder result = new StringBuilder("响应时间格式化测试:\n");

        // 测试各种情况
        result.append("Double.MAX_VALUE: ").append(ApiRequestUtil.formatResponseTime(Double.MAX_VALUE)).append("\n");
        result.append("0.0: ").append(ApiRequestUtil.formatResponseTime(0.0)).append("\n");
        result.append("1.234: ").append(ApiRequestUtil.formatResponseTime(1.234)).append("\n");
        result.append("0.05: ").append(ApiRequestUtil.formatResponseTime(0.05)).append("\n");
        result.append("10.999: ").append(ApiRequestUtil.formatResponseTime(10.999)).append("\n");

        // 测试API状态创建和显示
        ApiStatus testStatus = new ApiStatus();
        result.append("\n新创建的ApiStatus:\n");
        result.append("平均响应时间: ").append(ApiRequestUtil.formatResponseTime(testStatus.getAverageResponseTime())).append("\n");
        result.append("最后响应时间: ").append(ApiRequestUtil.formatResponseTime(testStatus.getLastResponseTime())).append("\n");

        // 模拟成功更新
        testStatus.updateOnSuccess(1.5);
        result.append("\n更新成功后:\n");
        result.append("平均响应时间: ").append(ApiRequestUtil.formatResponseTime(testStatus.getAverageResponseTime())).append("\n");
        result.append("最后响应时间: ").append(ApiRequestUtil.formatResponseTime(testStatus.getLastResponseTime())).append("\n");

        return result.toString();
    }
}
